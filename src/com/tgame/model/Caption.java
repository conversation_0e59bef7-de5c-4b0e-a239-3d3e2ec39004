package com.tgame.model;

import beatdz.LangLa_hg;
import com.badlogic.gdx.Gdx;
import com.badlogic.gdx.Application.ApplicationType;

public class Caption {
   public static String[] a;
   public static String[] b;
   public static String c;
   public static String d;
   public static String e;
   public static String f;
   public static String g;
   public static String h;
   public static String i;
   public static String j;
   public static String k;
   public static String l;
   public static String m;
   public static String n;
   public static String o;
   public static String p;
   public static String q;
   public static String r;
   public static String s;
   public static String t;
   public static String u;
   public static String v;
   public static String w;
   public static String x;
   public static String y;
   public static String z;
   public static String A;
   public static String B;
   public static String C;
   public static String D;
   public static String E;
   public static String F;
   public static String G;
   public static String H;
   public static String I;
   public static String J;
   public static String K;
   public static String L;
   public static String M;
   public static String N;
   public static String O;
   public static String P;
   public static String Q;
   public static String R;
   public static String S;
   public static String T;
   public static String U;
   public static String V;
   public static String W;
   public static String X;
   public static String Y;
   public static String Z;
   public static String aa;
   public static String ab;
   public static String ac;
   public static String ad;
   public static String ae;
   public static String af;
   public static String ag;
   public static String ah;
   public static String ai;
   public static String aj;
   public static String ak;
   public static String al;
   public static String am;
   public static String an;
   public static String ao;
   public static String ap;
   public static String aq;
   public static String ar;
   public static String as;
   public static String at;
   public static String au;
   public static String av;
   public static String aw;
   public static String ax;
   public static String ay;
   public static String az;
   public static String aA;
   public static String aB;
   public static String aC;
   public static String aD;
   public static String aE;
   public static String aF;
   public static String aG;
   public static String aH;
   public static String aI;
   public static String aJ;
   public static String aK;
   public static String aL;
   public static String aM;
   public static String aN;
   public static String aO;
   public static String aP;
   public static String aQ;
   public static String aR;
   public static String aS;
   public static String aT;
   public static String aU;
   public static String aV;
   public static String aW;
   public static String aX;
   public static String aY;
   public static String aZ;
   public static String ba;
   public static String bb;
   public static String bc;
   public static String bd;
   public static String be;
   public static String bf;
   public static String bg;
   public static String bh;
   public static String bi;
   public static String bj;
   public static String bk;
   public static String bl;
   public static String bm;
   public static String bn;
   public static String bo;
   public static String bp;
   public static String bq;
   public static String br;
   public static String bs;
   public static String bt;
   public static String bu;
   public static String bv;
   public static String bw;
   public static String bx;
   public static String by;
   public static String bz;
   public static String bA;
   public static String bB;
   public static String bC;
   public static String bD;
   public static String bE;
   public static String bF;
   public static String bG;
   public static String bH;
   public static String bI;
   public static String bJ;
   public static String bK;
   public static String bL;
   public static String bM;
   public static String bN;
   public static String bO;
   public static String bP;
   public static String bQ;
   public static String bR;
   public static String bS;
   public static String bT;
   public static String bU;
   public static String bV;
   public static String bW;
   public static String bX;
   public static String bY;
   public static String bZ;
   public static String ca;
   public static String cb;
   public static String cc;
   public static String cd;
   public static String ce;
   public static String cf;
   public static String cg;
   public static String ch;
   public static String ci;
   public static String cj;
   public static String ck;
   public static String cl;
   public static String cm;
   public static String cn;
   public static String co;
   public static String cp;
   public static String cq;
   public static String cr;
   public static String cs;
   public static String ct;
   public static String cu;
   public static String cv;
   public static String cw;
   public static String cx;
   public static String cy;
   public static String cz;
   public static String cA;
   public static String cB;
   public static String cC;
   public static String cD;
   public static String cE;
   public static String cF;
   public static String cG;
   public static String cH;
   public static String cI;
   public static String cJ;
   public static String cK;
   public static String cL;
   public static String cM;
   public static String cN;
   public static String cO;
   public static String cP;
   public static String cQ;
   public static String cR;
   public static String cS;
   public static String cT;
   public static String cU;
   public static String cV;
   public static String cW;
   public static String cX;
   public static String cY;
   public static String cZ;
   public static String da;
   public static String db;
   public static String dc;
   public static String dd;
   public static String de;
   public static String df;
   public static String dg;
   public static String dh;
   public static String di;
   public static String dj;
   public static String dk;
   public static String dl;
   public static String dm;
   public static String dn;
   public static String do_;
   public static String dp;
   public static String dq;
   public static String dr;
   public static String ds;
   public static String dt;
   public static String du;
   public static String dv;
   public static String dw;
   public static String dx;
   public static String dy;
   public static String dz;
   public static String dA;
   public static String dB;
   public static String dC;
   public static String dD;
   public static String dE;
   public static String dF;
   public static String dG;
   public static String dH;
   public static String dI;
   public static String dJ;
   public static String dK;
   public static String dL;
   public static String dM;
   public static String dN;
   public static String dO;
   public static String dP;
   public static String dQ;
   public static String dR;
   public static String dS;
   public static String dT;
   public static String dU;
   public static String dV;
   public static String dW;
   public static String dX;
   public static String dY;
   public static String dZ;
   public static String ea;
   public static String eb;
   public static String ec;
   public static String ed;
   public static String ee;
   public static String ef;
   public static String eg;
   public static String eh;
   public static String ei;
   public static String ej;
   public static String ek;
   public static String el;
   public static String em;
   public static String en;
   public static String eo;
   public static String ep;
   public static String eq;
   public static String er;
   public static String es;
   public static String et;
   public static String eu;
   public static String ev;
   public static String ew;
   public static String ex;
   public static String ey;
   public static String ez;
   public static String eA;
   public static String eB;
   public static String eC;
   public static String eD;
   public static String eE;
   public static String eF;
   public static String eG;
   public static String eH;
   public static String eI;
   public static String eJ;
   public static String eK;
   public static String eL;
   public static String eM;
   public static String eN;
   public static String eO;
   public static String eP;
   public static String eQ;
   public static String eR;
   public static String eS;
   public static String eT;
   public static String eU;
   public static String eV;
   public static String eW;
   public static String eX;
   public static String eY;
   public static String eZ;
   public static String fa;
   public static String fb;
   public static String fc;
   public static String fd;
   public static String fe;
   public static String ff;
   public static String fg;
   public static String fh;
   public static String fi;
   public static String fj;
   public static String fk;
   public static String fl;
   public static String fm;
   public static String fn;
   public static String fo;
   public static String fp;
   public static String fq;
   public static String fr;
   public static String fs;
   public static String ft;
   public static String fu;
   public static String fv;
   public static String fw;
   public static String fx;
   public static String fy;
   public static String fz;
   public static String fA;
   public static String fB;
   public static String fC;
   public static String fD;
   public static String fE;
   public static String fF;
   public static String fG;
   public static String fH;
   public static String fI;
   public static String fJ;
   public static String fK;
   public static String fL;
   public static String fM;
   public static String fN;
   public static String fO;
   public static String fP;
   public static String fQ;
   public static String fR;
   public static String fS;
   public static String fT;
   public static String fU;
   public static String fV;
   public static String fW;
   public static String fX;
   public static String fY;
   public static String fZ;
   public static String ga;
   public static String gb;
   public static String gc;
   public static String gd;
   public static String ge;
   public static String gf;
   public static String gg;
   public static String gh;
   public static String gi;
   public static String gj;
   public static String gk;
   public static String gl;
   public static String gm;
   public static String gn;
   public static String go;
   public static String gp;
   public static String gq;
   public static String gr;
   public static String gs;
   public static String gt;
   public static String gu;
   public static String gv;
   public static String gw;
   public static String gx;
   public static String gy;
   public static String gz;
   public static String gA;
   public static String gB;
   public static String gC;
   public static String gD;
   public static String gE;
   public static String gF;
   public static String gG;
   public static String gH;
   public static String gI;
   public static String gJ;
   public static String gK;
   public static String gL;
   public static String gM;
   public static String gN;
   public static String gO;
   public static String gP;
   public static String gQ;
   public static String gR;
   public static String gS;
   public static String gT;
   public static String gU;
   public static String gV;
   public static String gW;
   public static String gX;
   public static String gY;
   public static String gZ;
   public static String ha;
   public static String hb;
   public static String hc;
   public static String hd;
   public static String he;
   public static String hf;
   public static String hg;
   public static String hh;
   public static String hi;
   public static String hj;
   public static String hk;
   public static String hl;
   public static String hm;
   public static String hn;
   public static String ho;
   public static String hp;
   public static String hq;
   public static String hr;
   public static String hs;
   public static String ht;
   public static String hu;
   public static String hv;
   public static String hw;
   public static String hx;
   public static String hy;
   public static String hz;
   public static String hA;
   public static String hB;
   public static String hC;
   public static String hD;
   public static String hE;
   public static String hF;
   public static String hG;
   public static String hH;
   public static String hI;
   public static String hJ;
   public static String hK;
   public static String hL;
   public static String hM;
   public static String hN;
   public static String hO;
   public static String hP;
   public static String hQ;
   public static String hR;
   public static String hS;
   public static String hT;
   public static String hU;
   public static String hV;
   public static String hW;
   public static String hX;
   public static String hY;
   public static String hZ;
   public static String ia;
   public static String ib;
   public static String ic;
   public static String id;
   public static String ie;
   public static String if_;
   public static String ig;
   public static String ih;
   public static String ii;
   public static String ij;
   public static String ik;
   public static String il;
   public static String im;
   public static String in;
   public static String io;
   public static String ip;
   public static String iq;
   public static String ir;
   public static String is;
   public static String it;
   public static String iu;
   public static String iv;
   public static String iw;
   public static String ix;
   public static String iy;
   public static String iz;
   public static String iA;
   public static String iB;
   public static String iC;
   public static String iD;
   public static String iE;
   public static String iF;
   public static String iG;
   public static String iH;
   public static String iI;
   public static String iJ;
   public static String iK;
   public static String iL;
   public static String iM;
   public static String iN;
   public static String iO;
   public static String iP;
   public static String iQ;
   public static String iR;
   public static String iS;
   public static String iT;
   public static String iU;
   public static String iV;
   public static String iW;
   public static String iX;
   public static String iY;
   public static String iZ;
   public static String ja;
   public static String jb;
   public static String jc;
   public static String jd;
   public static String je;
   public static String jf;
   public static String jg;
   public static String jh;
   public static String ji;
   public static String jj;
   public static String jk;
   public static String jl;
   public static String jm;
   public static String jn;
   public static String jo;
   public static String jp;
   public static String jq;
   public static String jr;
   public static String js;
   public static String jt;
   public static String ju;
   public static String jv;
   public static String jw;
   public static String jx;
   public static String jy;
   public static String jz;
   public static String jA;
   public static String jB;
   public static String jC;
   public static String jD;
   public static String jE;
   public static String jF;
   public static String jG;
   public static String jH;
   public static String jI;
   public static String jJ;
   public static String jK;
   public static String jL;
   public static String jM;
   public static String jN;
   public static String jO;
   public static String jP;
   public static String jQ;
   public static String jR;
   public static String jS;
   public static String jT;
   public static String jU;
   public static String jV;
   public static String jW;
   public static String jX;
   public static String jY;
   public static String jZ;
   public static String ka;
   public static String kb;
   public static String kc;
   public static String kd;
   public static String ke;
   public static String kf;
   public static String kg;
   public static String kh;
   public static String ki;
   public static String kj;
   public static String kk;
   public static String kl;
   public static String km;
   public static String kn;
   public static String ko;
   public static String kp;
   public static String kq;
   public static String kr;
   public static String ks;
   public static String kt;
   public static String ku;
   public static String kv;
   public static String kw;
   public static String kx;
   public static String ky;
   public static String kz;
   public static String kA;
   public static String kB;
   public static String kC;
   public static String kD;
   public static String kE;
   public static String kF;
   public static String kG;
   public static String kH;
   public static String kI;
   public static String kJ;
   public static String kK;
   public static String kL;
   public static String kM;
   public static String kN;
   public static String kO;
   public static String kP;
   public static String kQ;
   public static String kR;
   public static String kS;
   public static String kT;
   public static String kU;
   public static String kV;
   public static String kW;
   public static String kX;
   public static String kY;
   public static String kZ;
   public static String la;
   public static String lb;
   public static String lc;
   public static String ld;
   public static String le;
   public static String lf;
   public static String lg;
   public static String lh;
   public static String li;
   public static String lj;
   public static String lk;
   public static String ll;
   public static String lm;
   public static String ln;
   public static String lo;
   public static String lp;
   public static String lq;
   public static String lr;
   public static String ls;
   public static String lt;
   public static String lu;
   public static String lv;
   public static String lw;
   public static String lx;
   public static String ly;
   public static String lz;
   public static String lA;
   public static String lB;
   public static String lC;
   public static String lD;
   public static String lE;
   public static String lF;
   public static String lG;
   public static String lH;
   public static String lI;
   public static String lJ;
   public static String lK;
   public static String lL;
   public static String lM;
   public static String lN;
   public static String lO;
   public static String lP;
   public static String lQ;
   public static String lR;
   public static String lS;
   public static String lT;
   public static String lU;
   public static String lV;
   public static String lW;
   public static String lX;
   public static String lY;
   public static String lZ;
   public static String ma;
   public static String mb;
   public static String mc;
   public static String md;
   public static String me;
   public static String mf;
   public static String mg;
   public static String mh;
   public static String mi;
   public static String mj;
   public static String mk;
   public static String ml;
   public static String mm;
   public static String mn;
   public static String mo;
   public static String mp;
   public static String mq;
   public static String mr;
   public static String ms;
   public static String mt;
   public static String mu;
   public static String mv;
   public static String mw;
   public static String mx;
   public static String my;
   public static String mz;
   public static String mA;
   public static String mB;
   public static String mC;
   public static String mD;
   public static String mE;
   public static String mF;
   public static String mG;
   public static String mH;
   public static String mI;
   public static String mJ;
   public static String mK;
   public static String mL;
   public static String mM;
   public static String mN;
   public static String mO;
   public static String mP;
   public static String mQ;
   public static String mR;
   public static String mS;
   public static String mT;
   public static String mU;
   public static String mV;
   public static String mW;
   public static String mX;
   public static String mY;
   public static String mZ;
   public static String na;
   public static String nb;
   public static String nc;
   public static String nd;
   public static String ne;
   public static String nf;
   public static String ng;
   public static String nh;
   public static String ni;
   public static String nj;
   public static String nk;
   public static String nl;
   public static String nm;
   public static String nn;
   public static String no;
   public static String np;
   public static String nq;
   public static String nr;
   public static String ns;
   public static String nt;
   public static String nu;
   public static String nv;
   public static String nw;
   public static String nx;
   public static String ny;
   public static String nz;
   public static String nA;
   public static String nB;
   public static String nC;
   public static String nD;
   public static String nE;
   public static String nF;
   public static String nG;
   public static String nH;
   public static String nI;
   public static String nJ;
   public static String nK;
   public static String nL;
   public static String nM;
   public static String nN;
   public static String nO;
   public static String nP;
   public static String nQ;
   public static String nR;
   public static String nS;
   public static String nT;
   public static String nU;
   public static String nV;
   public static String nW;
   public static String nX;
   public static String nY;
   public static String nZ;
   public static String oa;
   public static String ob;
   public static String oc;
   public static String od;
   public static String oe;
   public static String of;
   public static String og;
   public static String oh;
   public static String oi;
   public static String oj;
   public static String ok;
   public static String ol;
   public static String om;
   public static String on;
   public static String oo;
   public static String op;
   public static String oq;
   public static String or;
   public static String os;
   public static String ot;
   public static String ou;
   public static String ov;
   public static String ow;
   public static String ox;
   public static String oy;
   public static String oz;
   public static String oA;
   public static String oB;
   public static String oC;
   public static String oD;
   public static String oE;
   public static String oF;
   public static String oG;
   public static String oH;
   public static String oI;
   public static String oJ;
   public static String oK;
   public static String oL;
   public static String oM;
   public static String oN;
   public static String oO;
   public static String oP;
   public static String oQ;
   public static String oR;
   public static String oS;
   public static String oT;
   public static String oU;
   public static String oV;
   public static String oW;
   public static String oX;
   public static String oY;
   public static String oZ;
   public static String pa;
   public static String pb;
   public static String pc;
   public static String pd;
   public static String pe;
   public static String pf;
   public static String pg;
   public static String ph;
   public static String pi;
   public static String pj;
   public static String pk;
   public static String pl;
   public static String pm;
   public static String pn;
   public static String po;
   public static String pp;
   public static String pq;
   public static String pr;
   public static String ps;
   public static String pt;
   public static String pu;
   public static String pv;
   public static String pw;
   public static String px;
   public static String py;
   public static String pz;
   public static String pA;
   public static String pB;
   public static String pC;
   public static String pD;
   public static String pE;
   public static String pF;
   public static String pG;
   public static String pH;
   public static String pI;
   public static String pJ;
   public static String pK;
   public static String pL;
   public static String pM;
   public static String pN;
   public static String pO;
   public static String pP;
   public static String pQ;
   public static String pR;
   public static String pS;
   public static String pT;
   public static String pU;
   public static String pV;
   public static String pW;
   public static String pX;
   public static String pY;
   public static String pZ;
   public static String qa;
   public static String qb;
   public static String qc;
   public static String qd;
   public static String qe;
   public static String qf;
   public static String qg;
   public static String qh;
   public static String qi;
   public static String qj;
   public static String qk;
   public static String ql;
   public static String qm;
   public static String qn;
   public static String qo;
   public static String qp;
   public static String qq;
   public static String qr;
   public static String qs;
   public static String qt;
   public static String qu;
   public static String qv;
   public static String qw;
   public static String qx;
   public static String qy;
   public static String qz;
   public static String qA;
   public static String qB;
   public static String qC;
   public static String qD;
   public static String qE;
   public static String qF;
   public static String qG;
   public static String qH;
   public static String qI;
   public static String qJ;
   public static String qK;
   public static String qL;
   public static String qM;
   public static String qN;
   public static String qO;
   public static String[][] qP;
   public static String[] qQ;
   public static String[] qR;
   public static String[] qS;
   public static String[] qT;
   public static String[] qU;
   public static String[] qV;
   public static String[] qW;
   public static String[] qX;
   public static String[] qY;
   public static String[] qZ;
   public static String[] ra;
   public static String[] rb;
   public static String[] rc;
   public static String[] rd;
   public static String[] re;
   public static String[] rf;
   public static String[] rg;
   public static String[] rh;
   public static String[] ri;
   public static String[] rj;
   public static String[] rk;
   public static String[] rl;
   public static String[] rm;
   public static String[] rn;
   public static String[] ro;
   public static String[] rp;
   public static String[] rq;
   public static String[] rr;
   public static String[] rs;
   public static String[] rt;
   public static String[] ru;
   public static String[] rv;
   public static String[] rw;
   public static String[] rx;
   public static String[] ry;
   public static String[] rz;
   public static String[] rA;
   public static String[] rB;
   public static String[] rC;
   public static String[] rD;
   public static String[] rE;
   public static String[] rF;
   public static String[] rG;
   public static String[] rH;
   public static String[] rI;
   public static String[] rJ;
   public static String[] rK;
   public static String[] rL;
   public static String[] rM;
   public static String[] rN;
   public static String[] rO;
   public static String[] rP;
   public static String[] rQ;
   public static String[] rR;
   public static String[] rS;
   public static String[] rT;
   public static String[] rU;
   public static String[] rV;
   public static String[] rW;
   public static String[] rX;
   public static String[] rY;
   public static String[] rZ;
   public static String[] sa;
   public static String[] sb;
   public static String[] sc;
   public static String[] sd;
   public static String[] se;
   public static String[] sf;
   public static String[] sg;
   public static String[] sh;
   public static String[] si;
   public static String[] sj;
   public static String[] sk;
   public static String[] sl;
   public static String[] sm;
   public static String[] sn;
   public static String[] so;
   public static String[] sp;
   public static String[] sq;
   public static String[] sr;
   public static String[] ss;
   public static String[] st;
   public static String[] su;
   public static String[] sv;
   public static String[] sw;
   public static String[] sx;
   public static String[] sy;
   public static String[] sz;
   public static String[] sA;
   public static String[] sB;
   public static String[] sC;
   public static String[] sD;
   public static String[] sE;
   public static String[] sF;
   public static String[] sG;
   public static String[] sH;
   public static String[] sI;
   public static String[] sJ;
   public static String[] sK;
   public static String[] sL;
   public static String[] sM;
   public static String[] sN;
   public static String[] sO;
   public static String[] sP;
   public static String[] sQ;
   public static String[] sR;
   public static String[] sS;
   public static String[] sT;
   public static String[] sU;
   public static String[] sV;
   public static String[] sW;
   public static String[] sX;
   public static String[] sY;
   public static String[] sZ;
   public static String[] ta;
   public static String[] tb;
   public static String[] tc;
   public static String[] td;
   public static String[] te;
   public static String[] tf;
   public static String[] tg;
   public static String[] th;
   public static String[] ti;
   public static String[] tj;
   public static String[] tk;
   public static String[] tl;
   public static String[] tm;
   public static String[] tn;
   public static String[] to;
   public static String[] tp;
   public static String[] tq;
   public static String[] tr;
   public static String[] ts;
   public static String[] tt;
   public static String[] tu;
   public static String[] tv;
   public static String[] tw;
   public static String[] tx;
   public static String[] ty;
   public static String[] tz;
   public static String[] tA;
   public static String[] tB;
   public static String[] tC;
   public static String[] tD;
   public static String[] tE;
   public static String[] tF;
   public static String[] tG;
   public static String[] tH;
   public static String[] tI;
   public static String[] tJ;

   public static String a() {
      return lv;
   }

   public static String check(String var0) {
      if (Gdx.app.getType() == ApplicationType.iOS) {
         for(int var1 = 0; var1 < a.length; ++var1) {
            if (var0.contains(a[var1])) {
               return var0.replaceAll(a[var1], b[var1]);
            }
         }
      }

      return var0;
   }

   public static void loadVN() {
      c = "Hoàn thành: ";
      d = "Mất kết nối với máy chủ.";
      e = "Máy chủ";
      f = "Nhắc nhở";
      g = "Xác nhận";
      h = "Danh sách";
      i = "Vui lòng chờ...";
      j = "Không thể kết nối đến máy chủ.\nĐường truyền Internet có vấn đề hoặc\nmáy chủ đang bảo trì.";
      k = "Bạn nhận được";
      l = "Tin nhắn";
      m = "Chọn loại tin sẽ hiển thị trong Kênh Chung:";
      n = "Mặc định";
      o = "Tự động cuộn.";
      p = "Bỏ tự động cuộn.";
      q = "Túi";
      r = "Sử dụng";
      s = "Vứt bỏ";
      t = "Tách";
      u = "Sắp xếp";
      v = "Hủy";
      w = "Lớp";
      x = "Phần thưởng";
      y = "Trả lời";
      z = "Xóa";
      A = "Hộp thư";
      B = "Gởi thư";
      C = "Quan hệ";
      D = "Nhiệm vụ";
      E = "Gia tộc";
      F = "Hảo hữu";
      G = "Cáo từ";
      H = "Trang bị";
      I = "Xem thử";
      J = "Chọn loại y phục";
      K = "Tự Động Đánh";
      L = "Tự Tìm Đường";
      M = "Tạo nhân vật";
      N = "Khi quay trúng cùng loại sẽ tăng thêm 1 sao phần thưởng";
      O = "- Khi đăng ký tài khoản, người sử dụng (NSD) nên\r\ncung cấp đầy đủ thông tin về số điện thoại di động,\r\nemail và những thông tin cá nhân khác … Đây không\r\nphải là những thông tin bắt buộc, nhưng khi có những\r\nrủi ro, mất mát sau này, chúng tôi chỉ tiếp nhận\r\nnhững trường hợp điền đúng và đầy đủ những thông\r\ntin trên.\r\n- NSD có trách nhiệm phải tự mình bảo quản mật\r\nkhẩu, nếu mật khẩu bị lộ ra ngoài dưới bất kỳ hình\r\nthức nào, chúng tôi sẽ không chịu trách nhiệm về mọi\r\ntổn thất phát sinh.\r\n- Tuyệt đối không sử dụng bất kỳ chương trình,\r\ncông cụ hay hình thức nào khác để can thiệp (hack,\r\ncheat...) vào các dịch vụ, trò chơi trong hệ thống. \r\n- Nghiêm cấm việc phát tán, truyền bá hay cổ vũ cho\r\nbất kỳ hoạt động nào nhằm can thiệp, phá hoại hay\r\nxâm nhập vào dữ liệu của các dịch vụ, trò chơi.\r\n- Không được có bất kỳ hành vi nào nhằm đăng nhập\r\ntrái phép hoặc tìm cách đăng nhập trái phép cũng như\r\ngây thiệt hại cho hệ thống máy chủ. \r\n- Tuyệt đối nghiêm cấm việc xúc phạm, nhạo báng\r\nngười khác dưới bất kỳ hình thức nào (nhạo báng,\r\nchê bai, kỳ thị tôn giáo, giới tính, sắc tộc….).\r\n- Tuyệt đối nghiêm cấm mọi hành vi mạo nhận hay cố\r\ný làm người khác tưởng lầm mình là một người chơi\r\nkhác trong hệ thống dịch vụ tài khoản. Mọi hành vi vi\r\nphạm sẽ bị xử lý hoặc xóa tài khoản.\r\n- Tuyệt đối nghiêm cấm mọi hành vi tuyên truyền,\r\nchống phá và xuyên tạc chính quyền, thể chế chính\r\ntrị, và các chính sách của nhà nước... \r\n- Không có những hành vi, thái độ làm tổn hại đến uy\r\ntín của các sản phẩm, dịch vụ, trò chơi dưới bất kỳ\r\nhình thức nào, phương thức nào. Mọi vi phạm sẽ bị\r\ntước bỏ mọi quyền lợi liên quan đối với tài khoản\r\nhoặc xử lý trước pháp luật nếu cần thiết.\r\n- Mọi thông tin cá nhân của NSD sẽ được chúng tôi\r\nbảo mật, không tiết lộ ra ngoài. Chúng tôi không bán\r\nhay trao đổi những thông tin này với bất kỳ một bên\r\nthứ ba nào khác.\r\n- Ban quản trị có toàn quyền xóa, sửa chữa hay thay\r\nđổi các dữ liệu, thông tin tài khoản của NSD trong các\r\ntrường hợp người đó vi phạm những qui định kể trên\r\nmà không cần sự đồng ý của người sử dụng.\r\n";
      P = "Vào Game";
      Q = "Đăng ký";
      R = "Gói Ưu Đãi Rank";
      S = "Khi quay trúng cùng loại sẽ tăng thêm 2 sao phần thưởng";
      T = "Khi quay trúng cùng loại sẽ tăng thêm 3 sao phần thưởng";
      U = "Đăng ký tài khoản";
      V = "Sự kiện";
      W = "Thông tin";
      X = "Khôi phục mật khẩu";
      Y = "Email:";
      Z = "Điều khoản";
      aa = "xem";
      ab = "Tạo mới";
      ac = "Trở về";
      ad = "Tên nhân vật";
      ae = "Thay đổi địa chỉ máy chủ";
      af = "Bạn phải đồng ý điều khoản khi đăng ký sử dụng tài khoản.";
      ag = "Cấp ";
      ah = "Mã xác nhận:";
      ai = "Mã khác";
      aj = "Thay đổi mật khẩu";
      ak = "Mật khẩu cũ:";
      al = "Mật khẩu mới:";
      am = "Chọn hệ";
      an = "Binh khí";
      ao = "Ngoại trang";
      ap = "Giá:";
      aq = "Nhân vật";
      ar = "Bán";
      as = "Mua";
      at = "Số lượng:";
      au = "Tổng:";
      av = "Hiện có:";
      aw = "Hoặc Tanto";
      ax = "Đai trán";
      ay = "Áo";
      az = "Bao tay";
      aA = "Quần";
      aB = "Giày";
      aC = "Vũ khí";
      aD = "Dây thừng";
      aE = "Móc sắt";
      aF = "Ống tiêu";
      aG = "Túi nhẫn giả";
      aH = "Thú nuôi";
      aI = "Áo choàng";
      aJ = "Bí kíp";
      aK = "Bùa nổ";
      aL = "Chọn loại phụ kiện";
      aM = "Không đủ";
      aN = "Không thể bán trang bị đã cường hóa.";
      aO = "Sau khi bán sẽ không thể phục hồi, bạn có đồng ý bán vật phẩm này không?";
      aP = "Đồng ý";
      aQ = "Trang bị sau khi sử dụng sẽ bị khóa, bạn có đồng ý sử dụng không?";
      aR = "Cất vào";
      aS = "Lấy ra";
      aT = "Hệ";
      aU = "Rương đồ";
      aV = "Ô sử dụng:";
      aW = "Đặt vào đá và trang bị";
      aX = "- Tỉ lệ thành công:";
      aY = "- Tốn bạc:";
      aZ = "- Tốn bạc khóa:";
      ba = "bạc ngân sách";
      bb = "Đặt vào Bùa nổ và tinh thạch";
      bc = "Chọn hình thức:";
      bd = "Đặt vào trang bị cần tách cường hóa";
      be = "- Bạc khóa thu được:";
      bf = "Dịch chuyển";
      bg = "Đặt vào trang bị và bùa dịch chuyển";
      bh = "Đặt vào đá cần ghép";
      bi = "Ghép";
      bj = "Nâng cấp";
      bk = "Chọn";
      bl = "Ô đá đã đầy.";
      bm = "- Ghép thành đá cấp";
      bn = "Bùa cường hóa";
      bo = "Đá vượt mức tối đa.";
      bp = "Đặt vào ít nhất 2 đá thì mới có thể ghép được.";
      bq = "Đá sau khi ghép sẽ bị khóa, bạn có muốn tiếp tục không?";
      br = "Sử dụng bạc";
      bs = "Sử dụng bạc khoá";
      bt = "Bùa";
      bu = "Ô tinh thạch đã đầy.";
      bv = "Cửa hàng";
      bw = "Chợ";
      bx = "Chọn vật phẩm cần bán";
      by = "còn";
      bz = "Thông báo";
      bA = "Giao Dịch";
      bB = "Chuyển bạc";
      bC = "Nhập số bạc cần chuyển:";
      bD = "Ô giao dịch đã đầy.";
      bE = "Khóa";
      bF = "Chờ # giây";
      bG = "Cài đặt";
      bH = "Trang bị sau khi làm đồ dự phòng sẽ bị khóa, bạn có đồng ý không?";
      bI = "Tổ đội";
      bJ = "kinh nghiệm";
      bK = "Hệ thống";
      bL = "Nhận";
      bM = "Âm thanh nhạc nền";
      bN = "Âm thanh hiệu ứng";
      bO = "Rương vừa đẹp vừa cao quý\n";
      bP = "Xóa dữ liệu để tải lại dữ liệu từ đầu. Bạn có muốn thực hiện không ?";
      bQ = "Nạp vàng";
      bR = "Phím tắt";
      bS = "Không tìm thấy nhóm trong cùng khu vực.";
      bT = "Bảng giá trị quy đổi nạp:\r\n- 10.000 đồng nhận được 10 Vàng\r\n- 20.000 đồng nhận được 20 Vàng\r\n- 50.000 đồng nhận được 55 Vàng\r\n- 100.000 đồng nhận được 110 Vàng\r\n- 200.000 đồng nhận được 225 Vàng\r\n- 500.000 đồng nhận được 570 Vàng\r\n- 1.000.000 đồng nhận được 1.150 Vàng\r\n- 2.000.000 đồng nhận được 2.320 Vàng\r\n* Thẻ Megacard được tặng thêm 5% vàng\r\n* Thẻ FPT Gate được tặng thêm 3% vàng\r\n* Nạp lần đầu nhận được thêm 100% vàng khóa\r\nVí dụ: nạp 500.000 đồng sẽ nhận được 570 vàng và 570 vàng khóa";
      bU = "Báo lỗi";
      bV = "Nội dung:";
      bW = "Luyện Bí Kíp";
      bX = "Bàn phím ảo";
      bY = "Bàn phím hệ thống";
      bZ = "Ẩn người chơi khác";
      ca = "Ẩn hiệu ứng";
      cb = "Nhập tên bạn hữu muốn thêm:";
      cc = "Bảng xếp hạng";
      cd = "Hạng";
      ce = "Tên";
      cf = "Tích lũy";
      cg = "Đặt vào trang bị và ngọc khảm cùng loại";
      ch = "Làng Đá";
      ci = "Làng Lá";
      cj = "Xem thông tin";
      ck = "Kết bạn";
      cl = "Chat riêng";
      cm = "Cấp độ 10 trở đi mới được sử dụng chức năng này.";
      cn = "Nhập giá bán bạc khóa, từ 4.000-10.000/1 vàng";
      co = "1 vàng =";
      cp = "Bán với số lượng";
      cq = "Thu phí:";
      cr = "Hiện có:";
      cs = " bạc khóa";
      ct = "Hóa đơn:";
      cu = "- Tiêu hao ";
      cv = "- Thu về ";
      cw = " vàng";
      cx = "Nhận rương";
      cy = "Chiến thắng";
      cz = "Bắt đầu";
      cA = "Tiếp";
      cB = "Phần thưởng: Bách Bảo Rương";
      cC = "Thưởng: ";
      cD = " hoạt lực hoặc ";
      cE = " vỏ sò";
      cF = " bạc hoặc ";
      cG = " vàng khóa hoặc ";
      cH = " hoặc ";
      cI = " đá cấp ";
      cJ = "Bạn đã không may mắn";
      cK = "Quy đổi: ";
      cL = "Bạn bị trừ";
      cM = "Chưa có chiêu đánh.";
      cN = "Mỗi tài khoản chỉ được tạo tối đa 4 nhân vật.";
      cO = "Vui lòng chờ trong giây lát...";
      cP = "Cộng";
      cQ = "Hủy";
      cR = "Ẩn";
      cS = "Chế tạo";
      cT = "Phạm vi hỗ trợ đồng đội: ";
      cU = " giây";
      cV = "Hỗ trợ tự động";
      cW = "Cấp: ";
      cX = " (chưa học)";
      cY = "Đã đạt cấp tối đa";
      cZ = "Cấp hiện tại: ";
      da = "Cấp kế: ";
      db = "Xem thông tin";
      dc = " điểm";
      dd = "Hướng dẫn";
      de = "Hp: ";
      df = "Mp: ";
      dg = "Thông tin chiêu thức đã bị ẩn";
      dh = "- Cấp chế tạo: ";
      di = "- Kinh nghiệm chế tạo: ";
      dj = "Hiện có:";
      dk = " hoạt lực";
      dl = " tinh thạch không khóa";
      dm = "Khảm";
      dn = "cấp";
      do_ = "Đặt vào trang bị cần tách ngọc khảm";
      dp = ", cấp PK: ";
      dq = "Gia tộc: ";
      dr = "Thành viên";
      ds = "Tộc trưởng";
      dt = "Tộc phó";
      du = "Trưởng lão";
      dv = "Chức danh: ";
      dw = "Gia tộc: chưa có";
      dx = "Hệ thống";
      dy = "Xem tất cả";
      dz = "Thông báo";
      dA = "Nhắc nhở";
      dB = "Bạn có muốn xóa tất cả tin nhắn liên quan tới ";
      dC = " không?";
      dD = "Hảo hữu";
      dE = "Tổ đội";
      dF = "Mời vào nhóm";
      dG = "Gởi thư";
      dH = "Quay tiếp";
      dI = "Nhận thưởng";
      dJ = "Số lần quay: ";
      dK = "Tích lũy: ";
      dL = "Cấp tích lũy: ";
      dM = "Gia tộc";
      dN = "Bạn có muốn rời khỏi gia tộc không?";
      dO = "Bạn có muốn trục xuất ";
      dP = " ra khỏi gia tộc không?";
      dQ = "Bãi chức";
      dR = "Bổ nhiệm tộc phó";
      dS = "Bổ nhiệm trưởng lão";
      dT = "Trục xuất";
      dU = "Phát lương";
      dV = "Bỏ cấm chat";
      dW = "Cấm chat";
      dX = "Gởi thư";
      dY = "Thông báo";
      dZ = "Rút quỹ";
      ea = "Góp quỹ";
      eb = "Rời gia tộc";
      ec = "Cống hiến tuần: ";
      ed = "Cống hiến: ";
      ee = "Tên gia tộc: ";
      ef = "Cấp: ";
      eg = "Kinh nghiệm: ";
      eh = "chưa đạt";
      ei = "đạt yêu cầu";
      ej = "Thành viên: ";
      ek = "Ngân sách: ";
      el = "Số lần mở cửa ải: ";
      em = "Ngày thành lập: ";
      en = " bạc";
      eo = "Lỗi game";
      ep = "Lỗi thẻ ";
      eq = "Hình thức:";
      er = "Tiêu đề:";
      es = "- Dịch chuyển thành công.";
      et = "- Cấp cường hóa của 2 trang bị đã được chuyển đổi.";
      eu = "- 2 trang bị dịch chuyển phải cùng loại.";
      ev = "- Dịch chuyển trang bị cùng cấp, hoặc cao hơn.";
      ew = "Trang bị đã cường hóa.";
      ex = "Trang bị chưa cường hóa.";
      ey = "Bùa dịch chuyển.";
      ez = "Trang bị đã được dịch chuyển cường hóa.";
      eA = "Cần đặt vào đầy đủ trang bị và bùa dịch chuyển.";
      eB = "Không thể dịch chuyển 2 trang bị khác loại.";
      eC = "Chỉ được dịch chuyển trang bị cùng cấp, hoặc cao hơn.";
      eD = "Trang bị từ +15 trở lên phải dùng Bùa Dịch Chuyển (cao cấp).";
      eE = "Trang bị từ +11 trở lên phải dùng Bùa Dịch Chuyển (trung cấp).";
      eF = "Trang bị sau khi dịch chuyển sẽ bị khóa, bạn có muốn tiếp tục không?";
      eG = "Hồi sinh sẽ hao tốn 1 vàng khóa (nếu không đủ vàng khóa sẽ sử dụng vàng). Bạn có đồng ý hồi sinh tại chỗ không?";
      eH = "Bạn đã bị ";
      eI = " đánh trọng thương";
      eJ = "Bạn đã bị trọng thương";
      eK = "Chọn thao tác để tiếp tục";
      eL = "Thử lại";
      eM = "Thoát Game";
      eN = "- Đặt vào 1.000 mảnh bí kíp";
      eO = "- Lệ phí: # bạc";
      eP = "Tải về: ";
      eQ = "Phiên bản: ";
      eR = "Thử vận may";
      eS = "Né đòn";
      eT = "EXP ";
      eU = "Bạc ";
      eV = "Bạc khóa ";
      eW = "Vàng ";
      eX = "Vàng khóa ";
      eY = "Gởi bán";
      eZ = "Thu mua";
      fa = "Hủy giao dịch";
      fb = "Giao Dịch Bạc Khóa";
      fc = "STT";
      fd = "Người bán";
      fe = "Số bạc khóa";
      ff = "Tỉ lệ/1 vàng";
      fg = " bạc khóa";
      fh = "Quà tặng";
      fi = "Nhập mã quà tặng:";
      fj = "Hiện đang có: ";
      fk = "Nhập số bạc: ";
      fl = "Nhập thông tin chưa đúng";
      fm = "Thời gian ";
      fn = "Tích lũy: ";
      fo = "Chiến tích: ";
      fp = "Chờ ";
      fq = "Còn ";
      fr = "Làng Lá";
      fs = "Giá trị nhập chưa đúng.";
      ft = "Túi không đủ bạc để chuyển.";
      fu = "Rao bán";
      fv = "Phân phát";
      fw = "Hạn sử dụng: ";
      fx = "Lớp: ";
      fy = "Yêu cầu cấp: ";
      fz = "Yêu cầu ";
      fA = "Yêu cầu tài phú: ";
      fB = "Yêu cầu giới tính: ";
      fC = "Hệ: ";
      fD = "Đã khóa";
      fE = "Không khóa";
      fF = "Có thể đổi # Tinh thạch";
      fG = " ở Kinkaku";
      fH = "Có thể xếp chồng";
      fI = "Giá mua: ";
      fJ = "Giá bán: ";
      fK = "Kích độ tu luyện";
      fL = "Kích cường hóa";
      fM = "Kích ẩn: ";
      fN = "Địa điểm kho báu: ";
      fO = "Vị trí:";
      fP = "Giám định";
      fQ = "Dự phòng";
      fR = "Bạn có đồng ý vứt bỏ ";
      fS = "Ô bí kíp đã đầy.";
      fT = "Bạn có đồng ý mua vật phẩm với giá ";
      fU = "Ô chứa vật phẩm đã đầy.";
      fV = "GỞI";
      fW = "XONG";
      fX = "Thành lập gia tộc";
      fY = "Tên gia tộc:";
      fZ = "Chỉ hiện online";
      ga = "Dân số: ";
      gb = " (mời kết bạn)";
      gc = "Nhận lời kết bạn";
      gd = "Không đồng ý";
      ge = "Xóa kết bạn";
      gf = "Xóa thù địch";
      gg = "Chủ đề:";
      gh = "Người gởi:";
      gi = "Đính kèm:";
      gj = "Không thể xóa thư có vật phẩm đính kèm.";
      gk = "Thư sau khi xóa sẽ không thể phục hồi, bạn có muốn tiếp tục không?";
      gl = "Chủ đề rỗng";
      gm = "Nhập nội dung";
      gn = "Gởi";
      go = "Người nhận:";
      gp = "Vật phẩm:";
      gq = "Lệ phí:";
      gr = "Vào giao diện túi để chọn vật phẩm.";
      gs = "Nhập tên người nhận";
      gt = "Túi không đủ bạc để gởi, chỉ có thể chuyển tối đa # bạc";
      gu = "Chủ đề chưa đúng.";
      gv = "Tên người nhận chưa đúng.";
      gw = "Chọn hết";
      gx = "Xóa đã chọn";
      gy = "Xóa đã đọc";
      gz = "Viết thư";
      gA = "còn ";
      gB = "có đính kèm";
      gC = "Vui lòng nhận vật phẩm trong thư trước khi xóa.";
      gD = "Không có thư để xóa.";
      gE = "Nhiệm vụ";
      gF = "Nhập chủ đề";
      gG = "Khu vực ";
      gH = " đã được gắn vào phím tắt.";
      gI = "Tỷ võ";
      gJ = "Cừu sát";
      gK = "Xin vào gia tộc";
      gL = "Mời vào gia tộc";
      gM = "Danh sách NPC";
      gN = "Nhập tên gia tộc:";
      gO = "Nhập tên nhân vật muốn thách đấu:";
      gP = "Nhập số tiền cược:";
      gQ = "Tất cả trang bị dự phòng đã được sử dụng vào nhân vật và ngược lại.";
      gR = "Vật phẩm đã được gởi bán.";
      gS = "Đã gởi thư thành công.";
      gT = "' muốn mời bạn giao dịch, bạn có đồng ý không?";
      gU = "Giao dịch thành công.";
      gV = " đã gởi lời mời kết bạn.";
      gW = "Chiêu sẽ tự động sử dụng khi Hp giảm dưới 20%.";
      gX = "Cấp hiện tại chưa đạt yêu cầu để sử dụng.";
      gY = "Danh sách gia tộc";
      gZ = "Được khảm tối đa # loại ngọc";
      ha = "Mở rộng thêm 1 loại ngọc khảm vào tất cả trang bị, cần tiêu hao # vàng. Bạn có đồng ý không?";
      hb = "Mỗi nhân vật chỉ được mở tối đa thêm 3 loại ngọc khảm vào tất cả trang bị.";
      hc = "Đã mở thêm # loại ngọc khảm";
      hd = "Đã mở";
      he = "Mở ra sẽ nhận được ngẫu nhiên đá cấp 8 hoặc cấp 9 hoặc cấp 10.\nMỗi tuần mở tối đa được 8 rương";
      hf = "Đá vừa\n";
      hg = " mời bạn vào nhóm, bạn có đồng ý vào nhóm không?";
      hh = " muốn tỷ võ với bạn, bạn có đồng ý tỷ võ không?";
      hi = "- Thời gian đã rời mạng ";
      hj = "\n- Thời gian sử dụng Xích Linh Chi ";
      hk = "\n- Kinh nghiệm nhận được ";
      hl = "\n- Có thể sử dụng Xích Linh Chi để nhận được kinh nghiệm ủy thác trong lần đăng nhập này.";
      hm = " muốn tỷ võ lôi đài với bạn, bạn có đồng ý tỷ võ ở lôi đài không?";
      hn = " cấp ";
      ho = " tài phú ";
      hp = " là ";
      hq = " của gia tộc ";
      hr = ", muốn mời bạn gia nhập gia tộc, bạn có đồng ý không?";
      hs = " và bạn bắt đầu tỷ võ.";
      ht = "Bạn đã bật trạng thái cừu sát ";
      hu = " đã bắt đầu cừu sát bạn.";
      hv = "Cừu sát đã bị hủy.";
      hw = " đã hủy cừu sát.";
      hx = " và ";
      hy = " đã tỷ võ hòa nhau.";
      hz = " đã tỷ võ thắng ";
      hA = " muốn xin vào nhóm, bạn có đồng ý không?";
      hB = "Mở giao diện Thông tin/Tiềm năng để tăng điểm.";
      hC = "Mở giao diện Thông tin/Chiêu thức để tăng điểm.";
      hD = "hoạt lực.";
      hE = "Khu vực: ";
      hF = "Mới nhất";
      hG = "Giá";
      hH = "Loại vật phẩm";
      hI = "Thời gian bán";
      hJ = "Tên người bán";
      hK = "Tìm theo:";
      hL = "Sắp xếp theo:";
      hM = "Giá bán:";
      hN = "Thời gian:";
      hO = "Phí:";
      hP = "Người bán/Giá";
      hQ = "Vật phẩm đang bán";
      hR = "Thời gian";
      hS = "Mua";
      hT = "Giá bán chưa hợp lệ.";
      hU = "Không đủ bạc để trả phí.";
      hV = "Mỗi ngày chỉ được rao 1 lần miễn phí, nếu rao tiếp sẽ tốn 1 vàng. Bạn có đồng ý không?";
      hW = "Không đủ bạc để mua.";
      hX = "Bạn có đồng ý mua '";
      hY = "' với giá ";
      hZ = " bạc không?";
      ia = "Tinh Anh";
      ib = "Thủ Lĩnh";
      ic = "Đá nhỏ\n";
      id = " dẫn về";
      ie = " hộ tống";
      if_ = "Level";
      ig = "Nhập số vàng, chỉ được nhập số chẵn";
      ih = "Số vàng dùng để mua:";
      ii = "Hiện có:";
      ij = "Hình ảnh chất lượng cao";
      ik = "Hình ảnh chất lượng vừa";
      il = "Hình ảnh chất lượng thấp";
      im = "Hình ảnh chất lượng rất thấp";
      in = "Không tự hiện giao diện này lần sau";
      io = "Thay đổi chất lượng hình ảnh cần phải tải lại dữ liệu, bạn có muốn tiếp tục không?";
      ip = "Chất lượng hình ảnh";
      iq = "Yêu cầu RAM trống tổi thiểu để chơi game";
      ir = "(nếu không đủ RAM sẽ bị đóng ứng dụng)";
      is = "Người nhận lương: ";
      it = "Nhập tiền lương: ";
      iu = "Nhập thông tin chưa đúng";
      iv = "Vui lòng chờ trong giây lát...";
      iw = "Bạn có đồng ý thoát khỏi trò chơi không?";
      ix = "Ngân sách đang có: ";
      iy = "Nhập số bạc: ";
      iz = "Nhập thông tin chưa đúng";
      iA = "Hoàn Thành";
      iB = "Nhận";
      iC = "Hủy";
      iD = "Tách vật phẩm";
      iE = "Trang bị đã được tách cường hóa.";
      iF = "Đặt vào trang bị đã cường hóa.";
      iG = "Cấp cường hóa sẽ trở về +0 sau khi tách, bạn có muốn tiếp tục không?";
      iH = "Vật phẩm cần giao:";
      iI = "Chính: ";
      iJ = "- Tìm ";
      iK = "Đã làm hết nhiệm vụ";
      iL = " để nhận nhiệm vụ";
      iM = "- Level yêu cầu: ";
      iN = "Tuần hoàn ";
      iO = "Tách lấy ngọc khảm sẽ tiêu hao # vàng. Bạn có muốn tiếp tục không?";
      iP = " để nhận phần thưởng";
      iQ = "Thu phục ";
      iR = "Không có nhiệm vụ để nhận";
      iS = "Hủy nhiệm vụ";
      iT = "Bạn đã hoàn thành tất cả nhiệm vụ, nhiệm vụ mới sẽ được cập nhật ở phiên bản tiếp theo.";
      iU = "Nhiệm vụ: ";
      iV = "Ghi chú: đi tìm ";
      iW = "  - Level yêu cầu: ";
      iX = "Ghi chú: nhiệm vụ đã hoàn thành hãy đi tìm ";
      iY = "Bạn có đồng ý hủy bỏ nhiệm vụ này không?";
      iZ = "Phần thưởng: ";
      ja = "Nhóm";
      jb = "Rời nhóm";
      jc = "Khóa nhóm";
      jd = "Tự cho vào nhóm";
      je = "Tìm";
      jf = "Tạo nhóm";
      jg = "Giải tán";
      jh = "Chat nhóm";
      ji = "Nhóm trưởng";
      jj = "(đã đầy)";
      jk = " thành viên)";
      jl = "Nhường trưởng nhóm";
      jm = "Đuổi khỏi nhóm";
      jn = "Xin vào nhóm";
      jo = "Nhập vào câu thông báo:";
      jp = "Nhập thông tin chưa đúng";
      jq = "Lên Cấp";
      jr = "Hòa";
      js = "Thắng";
      jt = "Thua";
      ju = "Cấp";
      jv = "Tên";
      jw = "Chiến tích";
      jx = "Tài phú";
      jy = "Chuyên cần";
      jz = "Luyện tập";
      jA = "Lớp";
      jB = "Gia tộc";
      jC = " bạc khóa.";
      jD = "Cần ";
      jE = "- Trang bị đã cường hóa tối đa.";
      jF = "- Cường hóa thành công.";
      jG = "- Trang bị được lên cấp.";
      jH = "- Cường hóa thất bại.";
      jI = "- Trang bị không đổi.";
      jJ = "- Trang bị giảm cấp cường hóa.";
      jK = "Đặt vào trang bị cần cường hóa.";
      jL = "Tỉ lệ thành công ít nhất từ 10% trở lên mới được cường hóa.";
      jM = "Sử dụng quá nhiều đá sẽ lãng phí, bạn có muốn tiếp tục không?";
      jN = "Không đủ bạc khóa sẽ dùng sang bạc bạn có đồng ý không?";
      jO = "- Nâng cấp thành công.";
      jP = "Cần đặt vào Bùa nổ và tinh thạch.";
      jQ = "Đã nâng cấp tối đa chỉ số.";
      jR = "Luyện";
      jS = "Đặt vào bí kíp cần luyện";
      jT = "Đã luyện thành bí kíp mới.";
      jU = "- Cần bỏ vào 2 cuốn bí kíp khác loại";
      jV = "- Bí kíp phải được tu luyện tối đa";
      jW = "- Dùng loại sơ để luyện trung, loại trung để luyện cao";
      jX = "Bạn có thật sự muốn luyện bí kíp không?";
      jY = "Tự chọn";
      jZ = "ngày ";
      ka = "giờ ";
      kb = "phút";
      kc = "giờ";
      kd = " ngày ";
      ke = "giây";
      kf = "Trận đấu";
      kg = "thắng";
      kh = "thua";
      ki = "Thời gian thi đấu ";
      kj = " còn ";
      kk = "Vui lòng chọn mức nạp";
      kl = "Tăng tương khắc";
      km = "Giảm tương khắc";
      kn = "Tích lũy nạp";
      ko = "Bạn đã tích lũy # điểm nạp, qua ngày đầu tiên của tháng điểm tích lũy sẽ trở về 0. Sau khi nhận thưởng điểm tích lũy cũng sẽ bị giảm đi\n- 200.000: 20 vàng khóa\n- 500.000: 60 vàng khóa\n- 1.000.000: 150 vàng khóa\n- 2.000.000: 350 vàng khóa\n- 5.000.000: 1.000 vàng khóa\n- 10.000.000: 2.200 vàng khóa";
      kp = "- Khảm ngọc thành công.";
      kq = "Đá lớn\n";
      kr = "- Cần # ";
      ks = "- Cần 1 vũ khí Hokage cấp 40 trở lên";
      kt = "Đặt vào trang bị Hokage và ";
      ku = "Hiền Nhân";
      kv = "lệnh bài Myoboku";
      kw = "Thỏi bạc nhỏ\n";
      kx = "lệnh bài Kazekage";
      ky = "lệnh bài Tsuchikage";
      kz = "lệnh bài Mizukage";
      kA = "Cần đặt vào trang bị và ngọc khảm";
      kB = "Chọn trang bị có thể đổi tinh thạch";
      kC = "- Giờ chơi miễn phí: ";
      kD = "- Giờ chơi nạp: ";
      kE = "Cấp 40 trở lên mỗi ngày được tặng 18h";
      kF = "chơi miễn phí, nếu hết giờ chơi đánh quái";
      kG = "sẽ không rơi kinh nghiệm và bạc khóa...";
      kH = "Chọn trang bị Kazekage cần đổi";
      kI = "Chọn trang bị Hiền Nhân cần đổi";
      kJ = "Chọn trang bị Mizukage cần đổi";
      kK = "Chọn trang bị Tsuchikage cần đổi";
      kL = "Đã luyện thành công ";
      kM = "Thêm ô";
      kN = "Bảng Giá Nạp";
      kO = "Chưa có";
      kP = ", xin chào";
      kQ = "Mở rộng thêm 9 ô chứa vật phẩm cần hao tốn 90 vàng, bạn có đồng ý không?";
      kR = "Tài khoản: ";
      kS = "Nhân vật: ";
      kT = "Phiên bản: ";
      kU = "Áo Choàng Hokage";
      kV = "Tỷ Võ";
      kW = "Cừu Sát";
      kX = "Di chuyển cố định";
      kY = "Di chuyển tùy biến";
      kZ = "Xin chào";
      la = ", muốn xin gia nhập gia tộc, bạn có đồng ý không?";
      lb = "Đổi lệnh bài";
      lc = "Đổi hệ";
      ld = "Chọn trang bị Kazekage cần đổi";
      le = "Chọn trang bị Mizukage cần đổi";
      lf = "Chọn trang bị Tsuchikage cần đổi";
      lg = "Chọn trang bị Hiền Nhân cần đổi";
      lh = "Cần # vàng";
      li = "Chọn hệ muốn thay đổi";
      lj = "Không đủ Mp sử dụng chiêu";
      lk = "Sự dụng hỗ trợ";
      ll = "Phụ: ";
      lm = "Danh hiệu theo áo choàng";
      ln = "Vĩnh viễn";
      lo = "Thỏi bạc vừa\n";
      lp = "Phúc lợi";
      lq = "Nhận";
      lr = "Đủ bộ cường hóa cấp";
      ls = "Chưa đủ bộ cường hóa cấp 4";
      lt = "% bỏ qua kháng tính";
      lu = "Tăng thêm";
      lv = "Đại Chiến Làng Lá";
      lw = "Đề cử";
      lx = "Vứt bỏ vật phẩm sẽ tắt chế độ tự động nhặt vật phẩm. Bạn có đồng ý vứt bỏ vật phẩm không?";
      ly = "Tùy chỉnh tối ưu:";
      lz = "CHỌN MÁY CHỦ";
      lA = "vàng";
      lB = "Thời gian online hôm nay: ";
      lC = "Đã online liên tục: ";
      lD = "Nếu ngưng online số lần online sẽ trở về 1";
      lE = "Cấp hiện tại: ";
      lF = "Đã tích lũy nạp: ";
      lG = "Hôm nay bạn đã nạp: ";
      lH = "Tuần này bạn đã nạp: ";
      lI = "Hôm nay bạn đã tiêu xài: ";
      lJ = "Tuần này bạn đã tiêu xài: ";
      lK = "đã đạt cấp tối đa";
      lL = "Cần";
      lM = "ngọc khảm";
      lN = "Sử dụng nhiều hơn sẽ lãng phí";
      lO = "Tổng số Rank trong máy chủ: ";
      lP = "Khi đủ số lượng Rank tất cả nhân vật sẽ có quà";
      lQ = "Mở rộng";
      lR = "Cấp 40 trở lên thì mới được mở rộng thêm loại ngọc khảm vào tất cả các trang bị (tối đa mở được 3 lần)";
      lS = "Mở rộng thêm loại ngọc khảm thứ 2 vào tất cả các trang bị cần phải từ cấp 40 trở lên";
      lT = "Nhiệm vụ mỗi ngày";
      lU = "Khai mở";
      lV = "Không đủ bạc ngân sách để khai mở chiêu thức này.";
      lW = "Khai mở sẽ tiêu hao # bạc ngân sách. Bạn có đồng ý khai mở chiêu thức này không.";
      lX = "Cấp bang hội chưa đạt yêu cầu để khai mở chiêu thức này.";
      lY = "Bạn có muốn tự tìm đến vị trí của hoạt động \"#\" không?";
      lZ = "Sau khi gia nhập lớp học thì bấm vào đây sẽ có chức năng tự tìm đường đến nơi diễn ra hoạt động.";
      ma = "Tìm NPC";
      mb = "Sau khi gia nhập lớp học thì mới được sử dụng chức năng này.";
      mc = "Bạn có đồng ý đổi tài khoản không?";
      md = "Xin vào";
      me = "Xin";
      mf = "Sử dụng vật phẩm";
      mg = "SD nhiều";
      mh = "Số lượng sử dụng vật phẩm chưa đúng.";
      mi = "Bảo trì";
      mj = "Tốt";
      mk = "Chọn trang bị và đá để cường hóa";
      ml = "Không thể tìm thấy NPC, hiện tại chưa đến thời gian diễn ra hoạt động này.";
      mm = "Senju Tsunade đang ở gần đây, hãy đi tìm xung quanh";
      mn = "Không thể tìm thấy cao thủ nhẫn giả, hiện tại chưa đến thời gian diễn ra hoạt động này.";
      mo = "Cao thủ nhẫn giả đang ở gần đây, hãy đi tìm xung quanh. Nếu không tìm thấy hãy thử đổi kênh khu vực.";
      mp = "Sử dụng sẽ nhận được";
      mq = "Danh hiệu";
      mr = "Chọn đá để ghép";
      ms = "Chọn vật trang bị và ngọc khảm";
      mt = "Chọn bùa nổ và tinh thạch để nâng cấp";
      mu = "Chọn vật phẩm cần tách cường hóa";
      mv = "Chọn vật phẩm cần tách ngọc khảm";
      mw = "Sự kiện đua top này chưa mở";
      mx = "Rank # trở lên mới được sử dụng tính năng này";
      my = "Quà nạp";
      mz = "Quà Rank";
      mA = "Thẻ tháng";
      mB = "Đầu tư";
      mC = "Mua ngay";
      mD = "Bạn đã nạp liên tục: # ngày";
      mE = "Hạn chót tham gia: ";
      mF = "Hiện tại sự kiện này đã kết thúc";
      mG = "- Nạp 1 lần đúng theo các giá trị yêu cầu hoặc lớn";
      mH = "hơn các giá trị yêu cầu thì mới được nhận quà";
      mI = "- Nạp 1 lần đúng theo giá trị yêu cầu hoặc lớn";
      mJ = "giá trị yêu cầu thì mới được nhận quà";
      mK = "Chi tiết";
      mL = "Rank hiện tại cao nhất trong máy chủ: Rank";
      mM = "Số lần quay: ";
      mN = "Phần thưởng: ";
      mO = " * tỉ lệ quay được";
      mP = "Giá: 100 vàng";
      mQ = "Phần thưởng sau khi mua:";
      mR = " + Nhận ngay 100 vàng khóa";
      mS = " + Mỗi ngày nhận thêm 30 vàng khóa";
      mT = "     (nhận liên tục trong 30 ngày)";
      mU = "Hạn sử dụng: ";
      mV = "Giá: 300 vàng";
      mW = "Phần thưởng sau khi mua:";
      mX = " + Nhận ngay 300 vàng khóa";
      mY = " + Mỗi ngày nhận thêm 20 vàng khóa";
      mZ = "     (nhận vĩnh viễn)";
      na = "Mỗi ngày sẽ được nhận thêm 20 vàng khóa";
      nb = "Tổng số lần mua thẻ tháng trong máy chủ: ";
      nc = "Khi đủ số lần mua thẻ tất cả nhân vật sẽ có quà";
      nd = "Giá: 200 vàng";
      ne = "Đầu tư để được nhận thưởng";
      nf = "Tổng số đầu tư trong máy chủ: ";
      ng = "Khi đủ số tầu tư tất cả nhân vật sẽ có quà";
      nh = "Đã mua";
      ni = "\n- Chỉ cần trong máy chủ có nhân vật đạt cấp độ Rank theo yêu cầu thì tất cả các Rank trong máy chủ đều được nhận quà.\n- Cấp Rank càng cao thì nhận phần thưởng càng nhiều.";
      nj = "- Tích lũy điểm vòng quay để nhận số lần quay:\n   + 80 điểm: 1 lần quay\n   + 480 điểm: 2 lần quay\n   + 880 điểm: 3 lần quay\n   + 1.880 điểm: 4 lần quay\n   + 3.280 điểm: 5 lần quay\n- Phần thưởng khi tham gia vòng quay nạp:\n   + Quay lần 1: 80 vàng khóa * tỉ lệ quay\n   + Quay lần 2: 480 vàng khóa * tỉ lệ quay\n   + Quay lần 3: 880 vàng khóa * tỉ lệ quay\n   + Quay lần 4: 1.880 vàng khóa * tỉ lệ quay\n   + Quay lần 5: 3.280 vàng khóa * tỉ lệ quay\nLưu ý: khi nạp vàng trong thời gian diễn ra sự kiện\nvòng quay nạp thì điểm vòng quay mới được tính, sau\nkhi sự kiện kết thúc thì điểm vòng quay sẽ trở về 0.";
      nk = "c#cyanSắp diễn ra";
      nl = "c#orangeĐã kết thúc";
      nm = "c#yellowĐang diễn ra";
      nn = "Bắt đầu ";
      no = "Kết thúc ";
      np = "Ưu đãi Rank mở nhanh chức năng";
      nq = "Hiện tại chưa có sự kiện khuyến mãi giảm giá vật phẩm";
      nr = "- Phần thưởng: bí kíp cao cấp";
      ns = "Thỏi bạc lớn\n";
      nt = "Bình hoạt lực nhỏ\n";
      nu = "Bình hoạt lực vừa\n";
      nv = "Bình hoạt lực lớn\n";
      nw = "Vàng khóa nhỏ\n";
      nx = "Vàng khóa vừa\n";
      ny = "Vàng khóa lớn\n";
      nz = "Hoạt lực ";
      nA = "Nhấn 2 lần vào 1 vị trí map trên bản đồ sẽ tự tìm đường đến map đó";
      nB = "CLOSE";
      nC = "Tự Theo Sau";
      nD = "Sử dụng loa khi chat tin thế giới sẽ bị trừ 2 vàng.";
      nE = "Đặt vào ít nhất 2 vật phẩm cải trang khác loại";
      nF = "Chọn vật phẩm cải trang để ghép";
      nG = "- Lệ phí: # vàng";
      nH = "Cải trang sẽ bị khóa sau khi ghép, bạn có muốn tiếp tục không?";
      nI = "- Đã ghép thành công";
      nJ = "- Chọn vào cải trang rồi chọn nút";
      nK = " \"Mặc định\" để có được cải trang vừa ý";
      nL = "Đặt vào cải trang cần tách";
      nM = "Cải trang sau khi tách sẽ trở về trạng thái ban đầu, bạn có muốn tiếp tục không?";
      nN = "Chọn vật phẩm cải trang để tách";
      nO = "đã có trong ô ghép. Mỗi loại cải trang chỉ được chọn một lần.";
      nP = "- Đã tách thành công";
      nQ = "Chỉ được chọn cải trang đã được ghép";
      nR = "Trừ thêm 1%";
      nS = "bạc sau khi bán được";
      nT = "Phím gắn không hợp lệ! Chỉ sử dụng các ký tự A-Z.";
      nU = "Cấp vật phẩm";
      nV = "Điểm";
      nW = "Tự ghép thành";
      nX = "Không đủ bạc khóa để tự ghép đá";
      nY = "Sẽ tự động ghép khi số lượng đá trong túi có thỉ lệ ghép thành công từ 50% trở lên (chỉ dùng bạc khóa để ghép)";
      nZ = "Tất cả các trang bị không đủ điều kiện đổi tinh thạch sẽ được dọn sạch để ô túi được trống";
      oa = "Gõ phím trực tiếp";
      ob = "Số lượng: ";
      oc = "Áo Choàng";
      od = "Vận may";
      oe = "(Tối đa 120 thư, nếu nhiều hơn hệ thống sẽ tự xóa và không đền bù)";
      of = "Vào xem khu vực ";
      og = "- Đan dược sau khi chuyển đi sẽ bị mất";
      oh = "- Đan dược ở máy chủ Ngũ Kage chỉ";
      oi = "được sử dụng trong tuần, đừng chuyển";
      oj = "quá nhiều sẽ bị lãng phí.";
      ok = "(khóa)";
      ol = ", # ";
      om = "Tài khoản";
      on = "vật phẩm sự kiện";
      oo = "Hổ trợ treo máy";
      op = "Danh sách NPC";
      oq = "- Cần # bạc khóa";
      or = "Không có mục nào được chọn";
      os = "Xoá";
      ot = "Đăng ký tài khoản";
      ou = "Chiến Công";
      ov = "Byakugan";
      ow = "Sharingan";
      ox = "Rinnegan";
      oy = "- Cần trang bị Hokage (áo, giày, ống tiêu) trên cấp 40";
      oz = "Ô chứa ngọc đã đầy.";
      oA = "- Cần trang bị Hokage (quần, móc sắt, túi) trên cấp 40";
      oB = "- Cần trang bị Hokage (đai, bao tay, dây) trên cấp 40";
      oC = "Dịch chuyển nhanh";
      oD = "Nhường chức tộc trưởng";
      oE = "Cấp gia tộc càng cao, khi nhường chức tộc trưởng sẽ bị trừ ngân sách càng lớn. Bạn có muốn nhường chức tộc trưởng cho # không?";
      oF = "Khảm cấp 17: thu phí 30 vàng";
      oG = "Ô ghép đã tối đa.";
      oH = "- Các mốc chỉ nhận 1 lần duy nhất trong 1 đợt sự kiện";
      oI = "Chat tin liên server sẽ bị trừ 7 vàng.";
      oJ = "Chúc tết";
      oK = "Mừng tuổi";
      oL = "Khi sử dụng bùa cường hóa sẽ tăng thêm 3% tỉ lệ cường hóa, có thể đạt tối đa 100% tỉ lệ cường hóa";
      oM = "Nhận tất cả";
      oN = "- Bùa nổ đã nâng cấp tối đa";
      oO = "- Phần thưởng: bùa nổ cao cấp";
      oP = "Tích lũy: # điểm vòng quay";
      oQ = "Tìm trẻ lạc";
      oR = "Dẫn # về";
      oS = "Chơi lại";
      oT = "Không tìm thấy # trong khu vực này";
      oU = "Tự kết nối sau # giây";
      oV = "Mã bảo mật:";
      oW = "- Khóa rương";
      oX = "Quên mã";
      oY = "Kích hoạt";
      oZ = "Mở khóa";
      pa = "Đã mở";
      pb = "Xóa mã";
      pc = "Lưu và tự mở khóa trên thiết bị này";
      pd = "Nếu không mở khóa, mã sẽ tự xóa sau ";
      pe = "- Khóa nhân vật, dự phòng";
      pf = "- Khóa gia tộc";
      pg = "Mã bảo mật sẽ tự hủy sau 3 ngày, bạn có thật sự đã quên mã bảo mật hay không?";
      ph = "Bạn có đồng ý tạo mã bảo bảo vệ \"#\" để nhân vật được đảm bảo an toàn hay không?";
      pi = "Bạn có đồng ý xóa bỏ mã bảo vệ nhân vật hay không?";
      pj = "Bảo mật";
      pk = "Cất";
      pl = "Rút";
      pm = "Nhập số #: ";
      pn = "# đã yểm bùa uế thổ, nhập mã đã giải trừ bùa chú";
      po = "Xoá lời mời";
      pp = "Đã xoá # lời mời kết bạn";
      pq = "Tách tất cả";
      pr = "thử lại sau";
      ps = "Tự động kết nối, vui lòng chờ";
      pt = "Né Sát Thương";
      pu = "Thoát khỏi nơi này";
      pv = "Võ Đài";
      pw = "Vào xem";
      px = "Bạc cược";
      py = "Túi không còn bình Hp, hãy mua bình Hp tại các NPC Shizune ở các làng";
      pz = "Túi không còn bình Mp, hãy mua bình Mp tại các NPC Shizune ở các làng";
      pA = "Túi không còn thức ăn, hãy mua thức ăn tại các NPC Anko ở các làng";
      pB = "Tinh luyện";
      pC = "Đặt vào trang bị và đá tinh luyện";
      pD = "Đá tinh\nluyện";
      pE = "Tinh luyện";
      pF = "Trang bị đã được tinh luyện";
      pG = "- Mỗi lần chỉ được chọn một dòng chỉ số cần tinh luyện";
      pH = "- Dùng 1 đá tinh luyện sẽ ra chỉ số ngẫu nhiên";
      pI = "Bạn có đồng ý tinh luyện trang bị không?";
      pJ = "Tiếp tục";
      pK = "Bạn có đồng ý nhận tất cả thư không?";
      pL = "Thao tác đã được ghi nhận";
      pM = "Chỉ sử dụng được sau khi đã gia nhập lớp.";
      pN = " : gia tộc # chiếm đóng";
      pO = " : khu vực trung lập";
      pP = " (cấp #)";
      pQ = ": # lãnh thổ";
      pR = "Thống kê";
      pS = "Gia tộc tập luyện tại lãnh thổ đã chiếm sẽ tăng thêm 10% exp và 5% bạc khóa, các nhân vật khác ngoài gia tộc thì bị trừ 10% exp. Các map trung lập thì vẫn nhận đủ 100% exp khi đánh quái, xem thêm chi tiết tại menu Hoạt Động/Cẩm nang";
      pT = "Phong ấn";
      pU = "Tìm và phong ấn #";
      pV = "Cần phải sử dụng hộp sắc màu thì mới kích hoạt được tính năng này";
      pW = "Vui lòng cấp quyền truy cập thiết bị Micro.";
      pX = "Thiết bị Mirco chưa sẵn sàng.";
      pY = "Cần giữ nút Mirco ít nhất vài giây thì mới có thể ghi âm tin nhắn thoại";
      pZ = "Thiết bị Mirco chưa sẵn sàng, gắn hoặc cấp quyền cho Micro sau đó khởi động lại trò chơi thì mới có thể thu âm được.";
      qa = "Đang nhấn giữ nút Micro để ghi âm tin nhắn thoại...";
      qb = "Bỏ tự kết nối";
      qc = "Hoạt động dành cho gia tộc tranh đoạt\n lãnh thổ vĩ thú, xem chi tiết ở Cẩm Nang";
      qd = "Chưa có vĩ thú";
      qe = "Nâng cấp kỹ năng bằng bạc khoá hoặc vàng";
      qf = "dư ít nhất";
      qg = "TRÒ CHƠI CHẠY QUÁ TỐC ĐỘ CHO PHÉP";
      qh = "Xóa bỏ kỹ năng bằng bạc khoá hoặc vàng";
      qi = "Xóa bỏ";
      qj = "Tổ đội (#)";
      qk = "Tổ đội: ";
      ql = "Xem";
      qm = "Danh sách gửi gần đây";
      qn = "Xem thu gọn";
      qo = "- Dùng 50 đá tinh luyện sẽ ra chỉ số tối đa";
      qp = "cường hóa";
      qq = "Ở lại nơi này";
      qr = " (lượt thu nạp trong ngày còn #)";
      qs = "Mở giới hạn";
      qt = "Phân thân";
      qu = "Mở giới hạn của phân thân sẽ tiêu hao # vàng, bạn có đồng ý không?";
      qv = "Đổi sách";
      qw = "Sách chiến đấu cao cấp (1.500 vàng)";
      qx = "Sách chiến đấu siêu cấp (3.000 vàng)";
      qy = "Dùng để mở rộng thêm ô túi:";
      qz = "Bạn có đồng ý đăng xuất để chọn lại nhân vật khác không?";
      qA = "Bảng nhiệm vụ";
      qB = "Triệu hồi";
      qC = "Đã ghép với";
      qD = "Susano";
      qE = "Mỗi ngày chỉ được sử dụng 1 lần, bạn có đồng ý sử dụng không?";
      qF = "Mở rộng cấp của tất cả kỹ năng vĩ thú sẽ tiêu hao 2.000 vàng, bạn có đồng ý không?";
      qG = "Đang tải hình đẹp, vui lòng chờ trong ít phút";
      qH = "Lục Đạo";
      qI = "Đặt vào trang bị Hiền Nhân hoặc Sharingan hoặc Byakugan hoặc Rinnegan";
      qJ = "- Chọn đúng loại ngọc nâng cấp theo trang bị đặt vào";
      qK = "ngọc cùng loại với trang bị";
      qL = "Khảm cấp 18: thu phí 60 vàng";
      qM = "Sử dụng vàng để nâng cấp kỹ năng đặc biệt";
      qN = "1,500 vàng";
      qO = "Nhân 2 phúc lợi ngày đến ";
      qP = new String[][]{{"Trang bị", "Áo choàng, Tanto, Thú nuôi, Cải trang", "Chỉ số", "Bạc không khóa, Vàng khóa"}, {LangLa_hg.c() + "- NÂNG CẤP:\n" + LangLa_hg.a() + "+ Chọn hệ của trang bị theo \"Kích Ẩn\" và nên phân chia từng bộ tùy theo mục đích sử dụng.\n+ Ưu tiên các trang bị có thể đổi thành tinh thạch và trang bị Hokage ( NPC Jiraiya ở trường Konoha).\n+ Ưu tiên cường hóa vũ khí trước các trang bị khác và cường hóa theo các mốc \"Kích cường hóa\" của từng trang bị.\n* Tham gia đầy đủ các tính năng, hoạt động của game để có thể kiếm nguyên liệu liên quan 1 cách tối ưu nhất.\n" + LangLa_hg.c() + "- NGỌC KHẢM:\n" + LangLa_hg.a() + "+ Kim Cương: Kháng tất cả\n+ Hổ Phách: Tăng điểm tấn công\n+ Ngọc Bích: Tăng Né Tránh\n+ Hồng Ngọc: Tăng HP\n+ Hải Lam: Tăng chính xác\n+ Thạch Anh: Hồi HP\n+ Lục Bảo: Chí Mạng\n+ Lam Ngọc: Giảm sát thương(cả người và quái)\n *Nguồn ngọc khảm: Cấm thuật, Đại chiến nhẫn giả lần 4 liên server, Vòng Quay Bạc, Các phần quà trong \"Phúc Lợi\".", "- ÁO CHOÀNG: Nên mua áo choàng theo số điểm tài phú cao nhất mà nhân vật đang có.\n- TANTO: Ưu tiên tanto có chỉ số hút máu và cùng hệ với vũ khí.\n- THÚ NUÔI: Nên tích tinh thạch  1 lần để mua \"chim đại bàng\" luôn, sau đó có thể tham gia các sự kiện trong game để kiếm thú nuôi mạnh hơn.\n- CẢI TRANG: Ưu tiên các cải trang có chỉ số charka nhiều trước, sau đó mới tới các chỉ số khác.\n* Có thể mua bằng tinh thạch ở NPC Tobirama ở trường Konoha\n*Nguồn Tinh Thạch: Đổi điểm chuyên cần, Top 16 đại hội nhẫn giả liên server, Mở túi may mắn,  Đổi đồ tinh thạch, Mua trên chợ, Shop vàng và vàng khóa, Các phần quà trong \"Phúc Lợi\", Hoạt động gia tộc", LangLa_hg.c() + "1.CÁC CHỈ SỐ CHÍNH\n" + LangLa_hg.a() + "- Bỏ qua kháng tính: \n+  Bỏ qua tất cả kháng tính của đối phương\n+ Ví dụ: Bỏ qua kháng tính 3% có nghĩa là có 3% tỷ lệ sẽ bỏ qua tất cả kháng tính của đối phương(tất cả kháng của đối phương = 0)\n- Giảm tấn công  khi bị chí mạng:\n+ Giảm sức tấn công khi bị chí mạng lên nhân vật\n+ Ví dụ: Giảm tấn công bị chí mạng -5% có nghĩa là đối phương có điểm tấn công khi bị chí mạng là 170% thì sẽ giảm của xuống còn 165%\n- Giảm trừ sát thương\n+  Giảm trừ sát thương lên nhân vật(cả quái và người)\n+ Ví dụ: Giảm trừ sát thương: +500  có nghĩa là  khi bị người khác đánh lên mình với dame 1000 thì nhân vật sẽ được giảm còn 500\n- Phản đòn: \n+ Phản lại sát lượng sát thương đối phương đã gây trong đòn đánh đó\n+ Ví dụ: Phản đòn +1% có nghĩa là đòn đánh của đối phương có 100 sát thương thì phản lại 1 sát thương\n- Sát thương chuyển thành hồi HP\n+ Sát thương của bản thân sẽ chuyển thành HP\n+ Ví dụ: Sát thương chuyển thành Hồi HP +3% có nghĩa là đòn đánh gây lên đối phương được 100 dame thì sẽ được cộng thêm +3 HP\n- Chí mạng: Tăng điểm chí mạng cho nhân vật\n- Chính xác: Giúp nhân vật tăng thêm chính xác\n- Né tránh: Tăng né tránh cho nhân vật\n- Bỏ qua né tránh: Bỏ qua điểm né tránh của đối phương\n- Tăng tốc độ di chuyển: Giúp nhân vật di chuyển nhanh hơn\n- Tăng tấn công: Tăng điểm sức tấn công thêm cho nhân vật\n- Sát thương quái : Tăng sát thương khi đánh quái.\n" + LangLa_hg.c() + "2. HIỆU ỨNG:\n" + LangLa_hg.a() + " - Gây bỏng: Đối phương sẽ nhận sát thương gấp 2 lần\n - Gây trúng độc: Mỗi giây sẽ trừ HP của đối phương\n - Gây Choáng: Đối phương sẽ bị bất động\n - Đóng Băng: Làm đóng băng và làm chậm tốc độ di chuyển của đối phương\n - Gây suy yếu: Giảm kháng và lực tấn công lên nhân vật", "- Bạc không khóa: Đổi bạc, Trao đổi buôn bán, Khu rừng chết, Ải gia tộc, Cao thủ nhẫn giả...\n- Vàng khóa: Các phần quà trong \"Phúc Lợi\", Túi may mắn, Hoạt động gia tộc..."}};
      a = new String[]{"Anko Mitarashi", "Chiyo", "Ebisu", "Ginkaku", "karui", "Kinkaku", "Mei Terumi", "Onoki", "Rasa", "Sarutobi Hiruzen", "Shizume", "Hateke Kakashi", "Ibiki Morino", "Jiraiya", "Kamizuki", "Kotetsu", "Maito Gai", "Sarutobi Asuma", "Senju Tobirama", "Umino Iruka", "Yuhi Kurenai", "Konoha Utatane", "Konohamaru", "Udon", "Inari", "Haku", "Zabuza", "Jirochou", "Idate Morino", "Rokusho Aoi", "Gaara", "Akimichi Choji", "Rock Lee", "Shino", "Temari", "Pain", "Hoshigaki Kisame", "Deidara", "Uchiha Obito", "Uchiha Itachi", "Kakuzu", "Kankuro", "Konan", "Sasori", "Zetsu", "Hidan", "Hokage", "Kakashi", "Minato", "Hashirama", "Anbu", "Anbu Cội Rễ", "Sai", "Iruka", "Kurenai", "Asuma", "Gai", "Karin", "Suigetsu", "Juugo", "Yamato", "Kabuto", "Tsunade", "Orochimaru", "Hiruzen", "Tobirama", "Madara", "Seimei", "Obito Lục Đạo", "Bạch Zetsu", "Anbu Gấu", "Anbu Cộp", "Anbu Chim", "Anbu Gà", "Anbu Dê", "Killer B", "Cương Thi Itachi", "Madara Lục Đạo", "Tử Môn Gai", "Frankenstein", "Dracula"};
      b = new String[]{"Sanko", "Chirio", "Sensu", "Kankaku", "Larui", "Sinsasu", "Temuri", "Sunoki", "Sansa", "Nutoba", "Mazume", "Fugaki", "Murono", "Siriyo", "Namzuki", "Rofetsu", "Gya", "Doria", "Norabima", "Ultimo", "Nohara", "Unsane", "Tohamu", "Vdan", "Nasaki", "Hancho", "Kanuza", "Sochan", "Monoki", "Aoken", "Nasra", "Chofuo", "Rocken", "Chiory", "Tensan", "Pychain", "Kansan", "Denia", "Ochio", "NeoAchi", "Boruzu", "Nukaro", "Kaara", "Sosare", "Destsu", "Hichan", "Hogkake", "Nanashi", "Nasanto", "Hasama", "Abu", "Abu Cội Rễ", "Sasi", "Marina", "Nogetsu", "Nichan", "Ochima", "Misan", "Tingechu", "Kuugo", "Bamato", "Kututo", "Suden", "Michiru", "Suzen", "Tobarima", "Nasaka", "Seomei", "Abita Lục Đạo", "Bạch Detsu", "Abu Gấu", "Abu Cộp", "Abu Chim", "Abu Gà", "Abu Dê", "RoBee", "Cương Thi", "Nasaka Lục Đạo", "Tử Môn Gya", "Frankenstein", "Dracula"};
      qQ = new String[]{"Hiện tại", "Thế giới", "Lãnh thổ", "Tìm Nhanh"};
      qR = new String[]{"Tài khoản:", "Nhập Email/Số điện thoại"};
      qS = new String[]{"Mật khẩu:", "Nhập mật khẩu", "Máy chủ:"};
      qT = new String[]{ax, ay, az, aA, aB};
      qU = new String[]{aC, aD, aE, aF, aG};
      qV = new String[]{aH, aJ, aI, aK};
      qW = new String[]{"Tất cả", "Lớp kiếm", "Lớp ám khí", "Lớp gậy", "Lớp đao", "Lớp dao"};
      qX = new String[]{"8 giờ", "16 giờ", "24 giờ", "48 giờ", "72 giờ"};
      qY = new String[]{"Cao thủ", "Của cải", "Tài phú", "Chuyên cần", "Gia tộc"};
      qZ = new String[]{"Tin lân cận", "Tin thế giới", "Tin làng", "Tin lớp", "Tin gia tộc", "Tin nhóm", "Tin riêng", "Tin Liên Server", "Tin thế giới"};
      ra = new String[]{"Chung", "Riêng", "Gia tộc", "Nhóm", "Lớp"};
      rb = new String[]{"Nhân vật", "Dự phòng", "Tiềm năng", "Chiêu thức", "Tổng hợp"};
      rc = new String[]{"Điểm còn:", "Sức mạnh:", "Chakra:", "Mp cơ bản:", "Hp cơ bản:"};
      rd = new String[]{"Tấn công", "Tấn công quái", "Chính xác", "Bỏ qua né tránh", "Chí mạng", "Tấn công khi đánh chí mạng", "Tăng tấn công lên hệ Lôi", "Tăng tấn công lên hệ Thổ", "Tăng tấn công lên hệ Thủy", "Tăng tấn công lên hệ Hỏa", "Tăng tấn công lên hệ Phong", "Gây suy yếu", "Gây trúng độc", "Gây làm chậm", "Gây bỏng", "Gây choáng", "Bỏ qua kháng tính"};
      re = new String[]{"Kháng Lôi", "Kháng Thổ", "Kháng Thủy", "Kháng Hỏa", "Kháng Phong", "Giảm sát thương", "Tốc độ di chuyển", "Né tránh", "Phản đòn", "Phòng chí mạng", "Tương khắc lên hệ ", "Giảm tương khắc của hệ ", "Giảm gây suy yếu", "Giảm gây trúng độc", "Giảm gây làm chậm", "Giảm gây bỏng", "Giảm gây choáng", "Giảm trừ chí mạng"};
      rf = new String[]{"Tăng kinh nghiệm đánh quái", "Điểm tài phú", "Điểm chuyên cần", "Điểm chuyên cần tuần", "Điểm của cải", "Điểm của cải tuần", "Điểm cường hóa", "Điểm cường hóa tuần", "Điểm lôi đài tháng", "Điểm luyện tập", "Điểm nạp nhiều", "Điểm Hokage Đai Trán", "Điểm Hokage Áo", "Điểm Hokage Bao Tay", "Điểm Hokage Quần", "Điểm Hokage Giày", "Điểm Hokage Vũ Khí", "Điểm Hokage Dây Thừng", "Điểm Hokage Móc Sắt", "Điểm Hokage Ống Tiêu", "Điểm Hokage Túi Nhẫn Giả", "Sách tiềm năng (Sơ)", "Sách tiềm năng (Trung)", "Sách tiềm năng (Cao)", "Sách kỹ năng (Sơ)", "Sách kỹ năng (Trung)", "Sách kỹ năng (Cao)", "Bánh ít bảo"};
      rg = new String[]{"Tinh", "thạch", "Ngọc", "Khảm"};
      rh = new String[]{"Lớp: Kiếm\nĐịnh vị: Cận chiến\nHệ: Lôi\nMức thao tác: Khó\nThầy: Hatake Kakashi", "Lớp: Ám khí\nĐịnh vị: Viễn chiến\nHệ: Thổ\nMức thao tác: Dễ\nThầy: Sarutobi Asuma", "Lớp: Gậy\nĐịnh vị: Viễn chiến\nHệ: Thủy\nMức thao tác: Khó\nCô: Yuhi Kurenai", "Lớp: Đao\nĐịnh vị: Viễn chiến\nHệ: Hỏa\nMức thao tác: Vừa\nThầy: Maito Gai", "Lớp: Dao\nĐịnh vị: Cận chiến\nHệ: Phong\nMức thao tác: Vừa\nThầy: Umino Iruka"};
      ri = new String[]{"Ip:", "Port:"};
      rj = new String[]{"Them ban 1", "Them ban 2", "Them ban 3", "Them ban 4", "Them ban 5", "Them ban 6", "Them ban 7", "Them ban 8", "Them ban 9", "Them ban 10"};
      rk = new String[]{"Dược phẩm", "Dược phẩm (Bạc khóa)"};
      rl = new String[]{"Tạp hóa", "Tạp hóa (Bạc khóa)"};
      rm = new String[]{"Quán ăn", "Quán ăn (Bạc khóa)"};
      rn = new String[]{"Áo choàng", "Tanto", "Bí kíp", "Thú nuôi", "Cải trang"};
      ro = new String[]{"Lôi", "Thổ", "Thủy", "Hỏa", "Phong"};
      rp = new String[]{"Bạc", "Bạc khóa"};
      rq = new String[]{ax, aC, ay, aD, az, aE, aA, aF, aB, aG};
      rr = new String[]{"Nữ", "Nam"};
      rs = new String[]{"vàng", "vàng khóa", "bạc", "bạc khóa", "tinh thạch"};
      rt = new String[]{ax, ay, az, aA, aB};
      ru = new String[]{aD, aE, aF, aG};
      rv = new String[]{"Vàng", "Vàng khóa"};
      rw = new String[]{"tháng", "tuần", "ngày", "giờ", "phút", "giây"};
      rx = new String[]{"Ghép đá", "Cường hóa", "Nâng cấp bùa nổ", "Tách cường hóa", "Dịch chuyển trang bị", "Khảm ngọc", "Tách ngọc khảm", "Ghép cải trang", "Tách cải trang"};
      ry = new String[]{"Giá cũ: ", "Giá mới: ", "Còn: # lượt mua", "Hết hàng"};
      rz = new String[]{"- Sau khi tách cường hóa trang bị", "sẽ trở về trạng thái ban đầu"};
      rA = new String[]{"Khu vàng", "Vàng khóa", "Khu Rank", "Khuyến mãi"};
      rB = new String[]{"Mua vào", "Bán ra"};
      rC = new String[]{"gần", "hết giờ"};
      rD = new String[]{"Số vỏ sò hiện có:", "Mức cược: ", "Bắt đầu", "vỏ sò", "Thưởng", "Vỏ sò", "Tiếp", "", "", ""};
      rE = new String[]{"Chúc phúc", "Bắt đầu"};
      rF = new String[]{"Khu vực", "(đông người)", "(đã đầy)"};
      rG = new String[]{"Nhóm", "Tìm nhóm", "Tạo nhóm", "Tên:", "Lớp:", "Cấp:", "Rời nhóm", "Khóa nhóm", "Trục xuất", "Nhường", "Kết bạn", "Xem thông tin", "Nói chuyện", "Hiện tại bạn chưa gia nhập nhóm."};
      rH = new String[]{"Điểm còn:", "điểm", "Chiêu: ", "Level yêu cầu: ", "Cấp tối đa: ", "Cấp hiện tại: ", "Cấp kế tiếp: ", "Mp sử dụng: ", "Thời gian thi triển: ", "Cự ly đánh: ", "Mục tiêu tối đa: "};
      rI = new String[]{"Duy trì: ", "Còn lại: "};
      rJ = new String[]{"Thông tin", "Thành viên", "Vật phẩm", "Kỹ năng", "Xem log"};
      rK = new String[]{"Ngồi yên", "Về làng", "Quay về", "Hồi sinh"};
      rL = new String[]{"Bơm HP:", "Bơm MP:", "Phạm vi đánh:", "Sử dụng chiêu hỗ trợ", "Dùng thức ăn", "Dùng vàng hồi sinh", "Tự uống rượu", "Tự ghép đá (trên 50%)", "Tự quay về điểm chết", "Tự dọn rác", "Né Linh Thú"};
      rM = new String[]{"Sức mạnh: tăng tấn công (gợi ý tăng 35%)\nChakra: tăng tấn công, chí mạng, HP, MP, né tránh (gợi ý tăng 20%)\nMp cơ bản: tăng Mp (gợi ý tăng 10%)\nHp cơ bản: tăng Hp (gợi ý tăng 35%)\nDưới cấp 15 được tẩy điểm miễn phí tại giáo viên dạy võ.\nLưu ý: điểm chí mạng chỉ tối đa là 3.000, nếu vượt quá 3.000 thì cứ 1 điểm chí mạng sẽ được tăng thêm 2 điểm lực tấn công", "Không sử dụng sức mạnh để làm tấn công\nChakra: tăng tấn công, chí mạng, HP, MP, né tránh (gợi ý tăng 20%)\nMp cơ bản: tăng Mp và tấn công (gợi ý tăng 40%)\nHp cơ bản: tăng Hp (gợi ý tăng 40%)\nDưới cấp 15 được tẩy điểm miễn phí tại giáo viên dạy võ.\nLưu ý: điểm chí mạng chỉ tối đa là 3.000, nếu vượt quá 3.000 thì cứ 1 điểm chí mạng sẽ được tăng thêm 2 điểm lực tấn công"};
      rN = new String[]{"Tự động", "Nhặt đồ", "Cấu hình", "Phím tắt"};
      rO = new String[]{"Điểm Hokage Đai Trán", "Điểm Hokage Vũ khí", "Điểm Hokage Áo", "Điểm Hokage Dây Thừng", "Điểm Hokage Bao Tay", "Điểm Hokage Móc Sắt", "Điểm Hokage Quần", "Điểm Hokage Ống Tiêu", "Điểm Hokage Giày", "Điểm Hokage Túi Nhẫn giả"};
      rP = new String[]{"Tin lân cận", "Tin thế giới", "Tin Làng", "Tin hệ thống", "Tin gia tộc", "Tin nhóm", "Tin riêng", "Tin lớp", "Ẩn âm thanh chính mình", "Giảm lag tin chat", "Ẩn âm thanh thế giới", "Âm thanh nhạc nền", "Ẩn âm thanh gia tộc", "Âm thanh hiệu ứng"};
      rQ = new String[]{"Nhi đồng", "Luyện tập", "Cường hóa", "Nạp nhiều"};
      rR = new String[]{"Của cải", "Chuyên cần", "Cường hóa", "Cống hiến", "Lôi đài"};
      rS = new String[]{"Cải trang", "Tanto", "Thời trang"};
      rT = new String[]{"Tên", "Cấp", "Lớp", "Cấp PK", "Điểm tài phú", "Áo choàng", "Chuyên cần"};
      rU = new String[]{"RAM còn trống từ 350M trở lên", "RAM còn trống từ 450M trở lên", "RAM còn trống từ 550M trở lên", "RAM còn trống từ 750M trở lên", "RAM còn trống từ 850M trở lên", "RAM còn trống từ 1GB trở lên", "RAM còn trống từ 1,1GB trở lên", "RAM còn trống từ 1,2GB trở lên"};
      rV = new String[]{"Xóa dữ liệu", "Bàn phím", "Fanpage", "Cài đặt", "Âm Nhạc"};
      rW = new String[]{"Dược phẩm", "Tạp hóa", "Quán ăn", "Tuyệt thế lầu", "Binh khí", "Y phục", "Phụ kiện", "Rương đồ", "Binh khí (Chưa có lớp)", "Dã luyện đại sư", "Bách bảo rương", "Giao dịch", "Đổi tinh thạch", "Chúc phúc", "Bảng xếp hạng", "Mã quà tặng"};
      rX = new String[]{V, rV[3], "Nhận quà", "Đổi tài khoản", rV[1], "Thoát"};
      rY = new String[]{"Đá dưới cấp 2", "Đá dưới cấp 3", "Đá dưới cấp 4", "Đá dưới cấp 5", "Đá dưới cấp 6", "Đá dưới cấp 7", "Đá dưới cấp 8", "Đá dưới cấp 9", "Đá dưới cấp 10", "Đá dưới cấp 11", "Đá dưới cấp 12"};
      rZ = new String[]{""};
      sa = new String[]{"Vui lòng nhập thông tin thẻ cào", "Mã thẻ:", "Số serial:", "Bảng giá nạp", "Nhà mạng:"};
      sb = new String[]{"Tự động nhặt vật phẩm:", "Nhặt Đá", "Nhặt tất cả trang bị", "Nhặt vật phẩm sự kiện", "Nhặt vật phẩm khác", "Nhặt vật phẩm có giá trị"};
      sc = new String[]{"Bí", "kíp"};
      sd = new String[]{"Chiến trường"};
      se = new String[]{"Tất cả", "Làng Đá", "Làng Lá"};
      sf = new String[]{"100 vàng", "200 vàng", "300 vàng", "400 vàng", "500 vàng"};
      sg = new String[]{"Đổi tinh thạch", "Trang bị"};
      sh = new String[]{"Điểm", "Công", "Phòng"};
      si = new String[]{"Đổi Bí Kíp"};
      sj = new String[]{"Đồng đội", "Bạn bè", "Kẻ thù"};
      sk = new String[]{"Bạc"};
      sl = new String[]{"Giao Vật Phẩm"};
      sm = new String[]{"Chính tuyến", "Phụ tuyến", "Thành tựu"};
      sn = new String[]{"Nhóm riêng", "Tìm nhóm", "Gần đây"};
      so = new String[]{"Vòng loại"};
      sp = new String[]{"Google", "Thẻ cào"};
      sq = new String[]{"0.99 USD nhận được 20 Vàng", "4.99 USD nhận được 110 Vàng", "9.99 USD nhận được 225 Vàng", "19.99 USD nhận được 455 Vàng", "29.99 USD nhận được 685 Vàng", "49.99 USD nhận được 1155 Vàng", "99.99 USD nhận được 2325 Vàng"};
      sr = new String[]{"Cần 5 hoạt lực, 1 tinh thạch không khóa", "Cần 50 hoạt lực, 10 tinh thạch không khóa", "Cần 500 hoạt lực, 100 tinh thạch không khóa", "Cần 30 hoạt lực, 5 tinh thạch không khóa", "Cần 70 hoạt lực, 10 tinh thạch không khóa", "Cần 35 hoạt lực, 5 đá Byakugan", "Cần 35 hoạt lực, 5 đá Sharingan", "Cần 35 hoạt lực, 5 đá Rinnegan", "Cần 50 hoạt lực, 3 đá Myoboku", ""};
      ss = new String[]{"Kazekage", "Mizukage", "Tsuchikage", "Hiền Nhân"};
      st = new String[]{"Ngọc", "Myoboku"};
      su = new String[]{"Uchiha Sasuke", "Nara Shikamaru", "Inuzuka Kiba", "Hyuga Neji", "Uzumaki Naruto", "Haruno Sakura", "Yamanaka Ino", "Hyuga Hinata", "Tenten"};
      sv = new String[]{"Apple", "Thẻ cào"};
      sw = new String[]{"Cô", "Cậu"};
      sx = new String[]{"cô", "cậu"};
      sy = new String[]{"Đại tỷ", "Đại ca"};
      sz = new String[]{"đại tỷ", "đại ca"};
      sA = new String[]{"chị", "anh"};
      sB = new String[]{"cô", "thầy"};
      sC = new String[]{"Chất lượng hình ảnh", "Xóa dữ liệu", "Cài đặt", "Trang chủ", "Fanpage", "Đăng xuất", "Thoát game", "Nhiệm vụ", "Tổ đội", "Quan hệ", "Gia tộc", "Quà tặng", "Cài đặt", "Đổi nhân vật", "Bảo vệ tài khoản", "Xoá tài khoản"};
      sD = new String[]{"Danh hiệu", "Vĩ thú"};
      sE = new String[]{"Tinh thạch khóa", "Tinh thạch không khóa"};
      sF = new String[]{"Nhân vật", "Dự phòng"};
      sG = new String[]{"Thứ 2", "Thứ 3", "Thứ 4", "Thứ 5", "Thứ 6", "Thứ 7", "Chủ nhật"};
      sH = new String[]{"Online ngày", "Online 7 ngày", "Thăng cấp", "Tiêu ngày", "Tiêu tuần"};
      sI = new String[]{"Hoạt động", "Thông báo", "Cẩm nang", "Mạnh lên"};
      sJ = new String[]{"Tạp hóa (Rank 1)", "Xem thi đấu võ đài (Rank 1)", "Dược phẩm (Rank 2)", "Quán ăn (Rank 3)", "Rương đồ (Rank 4)", "Thợ rèn (Rank 5)"};
      sK = new String[]{"Đua Top", "Sự kiện", "Tuần&Tháng", "Thưởng"};
      sL = new String[]{"Nạp ngày", "Nạp tuần", "Nạp liên tục", "Nạp 3 mốc", "Nạp đơn"};
      sM = new String[]{"Thẻ tháng", "Thẻ vĩnh viễn", "Quà tất cả"};
      sN = new String[]{"Gói hào hoa", "Gói chí tôn", "Quà tất cả"};
      sO = new String[]{"Nạp Rank", "Rank chung", "Quà tất cả", "Vòng\nquay nạp"};
      sP = new String[]{"Bách bảo rương", "Chế tạo"};
      sQ = new String[]{"C.Trắng", "C.Xanh", "C.Đỏ"};
      sR = new String[]{"Của cải (tuần)", "Chuyên cần (tuần)", "Cường hóa (tuần)", "Cống hiến (tuần)", "Lôi đài (tháng)"};
      sS = new String[]{"Cao thủ 10x", "Cao thủ 20x", "Cao thủ 30x", "Cao thủ 40x", "Cao thủ 50x"};
      sT = new String[]{"Kiến Thức Nhẫn Giả", "Cao Thủ Nhẫn Giả", "Khu Rừng Chết", "Đại Hội Nhẫn Giả", "Đại Chiến Nhẫn Giả III", "Tranh đoạt lãnh thổ", "Câu cá cuối tuần", "Sơn Cáp Myoboku", "Đại Chiến Nhẫn Giả IV"};
      sU = new String[]{"Cấu hình", "Màn hình"};
      sV = new String[]{"Độ phân giải 800x480", "Độ phân giải 960x640", "Độ phân giải 1024x600", "Độ phân giải 1200x720", "Chọn để thay đổi kích thước màn hình"};
      sW = new String[]{"Góc nhìn rộng", "Tắt góc nhìn rộng cần tải lại dữ liệu. Bạn có đồng ý tải lại dữ liệu không?", "Góc nhìn rộng cần tải lại dữ liệu, khi chơi với góc nhìn rộng hình ảnh sẽ bị thu nhỏ lại (chỉ phù hợp với màn hình độ phân giải lớn). Bạn có đồng ý tải lại dữ liệu không?"};
      sX = new String[]{"LẦN TRƯỚC", "NEW", "HOT"};
      sY = new String[]{"Sử dụng bình HP cấp 60 trở xuống", "Sử dụng bình HP cấp 55 trở xuống", "Sử dụng bình HP cấp 50 trở xuống", "Sử dụng bình HP cấp 40 trở xuống", "Sử dụng bình HP cấp 30 trở xuống", "Sử dụng bình HP cấp 20 trở xuống", "Sử dụng bình HP cấp 10"};
      sZ = new String[]{"100", "150", "200", "250", "300", "400", "500", "600", "Không giới hạn"};
      ta = new String[]{"Ô đánh số 1", "Ô đánh số 2", "Ô đánh số 3", "Ô đánh số 4", "Ô đánh số 5", "Di chuyển lên", "Di chuyển xuống", "Di chuyển qua trái", "Di chuyển qua phải", "Thay đổi mục tiêu"};
      tb = new String[]{"Đá cấp 2 trở xuống", "Đá cấp 3 trở xuống", "Đá cấp 4 trở xuống", "Đá cấp 5 trở xuống", "Đá cấp 6 trở xuống", "Đá cấp 7 trở xuống", "Đá cấp 8 trở xuống", "Đá cấp 9 trở xuống", "Đá cấp 10 trở xuống"};
      tc = new String[]{"Sự kiện", "Thưởng"};
      td = new String[]{"Nhặt tất cả đá", "Nhặt đá cấp 2 trở lên", "Nhặt đá cấp 3 trở lên", "Nhặt đá cấp 4 trở lên", "Nhặt đá cấp 5 trở lên", "Nhặt đá cấp 6 trở lên", "Nhặt đá cấp 7 trở lên", "Nhặt đá cấp 8 trở lên", "Nhặt đá cấp 9 trở lên", "Nhặt đá cấp 10 trở lên", "Nhặt đá cấp 11 trở lên", "Chỉ nhặt đá cấp 12"};
      te = new String[]{"Tất cả", "Dưới 2 dòng", "Dưới 3 dòng", "Dưới 4 dòng", "Dưới 5 dòng", "Dưới 6 dòng", "Dưới 7 dòng", "Dưới 8 dòng", "Dưới 9 dòng", "Dưới 10 dòng", "Dưới 11 dòng", "Dưới 12 dòng", "Dưới 13 dòng", "Dưới 14 dòng", "Dưới 15 dòng"};
      tf = new String[]{"Chiến tích"};
      tg = new String[]{"Đan Dược"};
      th = new String[]{"(Cấp 1) Vỏ sò", "(Cấp 2) 10 vỏ sò", "(Cấp 3) Rương vỏ sò", "(Cấp 4) Hộp đạn dược cấp 1", "(Cấp 5) Hộp đạn dược cấp 2", "(Cấp 6) Ngọc Byakugan", "(Cấp 7) Ngọc Sharingan", "(Cấp 8) Ngọc Rinnegan", "(Cấp 9) Ngọc Myoboku"};
      ti = new String[]{"Ngọc", "Byakugan"};
      tj = new String[]{"Ngọc", "Sharingan"};
      tk = new String[]{"Ngọc", "Rinnegan"};
      tl = new String[]{"Chưa mở", "Chưa mở", "Chưa mở", "Chưa mở", "Chưa mở", "Chưa mở"};
      tm = new String[]{"Đổi bùa nổ", "Trang bị"};
      tn = new String[]{"Cập nhật dữ liệu #. Vui lòng kết nối", "Internet trước khi bắt đầu tải."};
      to = new String[]{"Không thể kết nối đến máy chủ.", "Đường truyền Internet có vấn đề hoặc", "máy chủ đang bảo trì."};
      tp = new String[]{"- Treo liên tục", "- Khởi động lại sau 30 phút khi đang treo", "- Khởi động lại sau 1 tiếng khi đang treo", "- Khởi động lại sau 2 tiếng khi đang treo", "- Khởi động lại sau 3 tiếng khi đang treo", "- Khởi động lại sau 4 tiếng khi đang treo", "- Khởi động lại sau 5 tiếng khi đang treo"};
      tq = new String[]{"Thức ăn", "Khu rừng chết"};
      tr = new String[]{"Tạp hoá", "Nhiệm vụ mỗi ngày"};
      ts = new String[]{"Di chuyển nhanh giữa các làng"};
      tt = new String[]{"Kiến thức nhẫn giả", "Nhận thưởng mỗi tuần", "Thử vận may"};
      tu = new String[]{"Rương đồ", "Ở lại nơi này"};
      tv = new String[]{"Đổi tinh thạch", "Đổi vàng"};
      tw = new String[]{"Nhiệm vụ tuần hoàn"};
      tx = new String[]{"Đại chiến nhẫn giả lần III", "Đại hội nhẫn giả", "Gia tộc", "Cấm thuật Izanami", "Trang bị Sharingan", "Liên Server"};
      ty = new String[]{"Địa cung", "Trang bị Rinnegan"};
      tz = new String[]{"Thu phục linh thú", "Tranh giành vĩ thú", "Võ đài"};
      tA = new String[]{"Ghép đá", "Cường hóa", "Nâng cấp bùa nổ", "Tách cường hóa", "Dịch chuyển trang bị", "Khảm ngọc", "Tách ngọc khảm", "Ghép cải trang", "Tách cải trang", "Tinh luyện"};
      tB = new String[]{"Bán dược phẩm", "Khu luyện tập", "Đại Chiến Nhẫn Giả Lần IV"};
      tC = new String[]{"50,000,000 bạc khoá", "1,000 vàng"};
      tD = new String[]{"30,000,000 bạc khoá", "500 vàng"};
      tE = new String[]{"- Màn hình chuẩn", "- Màn hình nhẹ", "- Màn hình đen"};
      tF = new String[]{"xin hãy nhận em làm đệ tử"};
      tG = new String[]{"Sora", "Inuzuka", "Yamato", "Karu", "Kikunojou", "Kuranai", "Haruna", "Tamanaka", "Yakomo"};
      tH = new String[]{"Lớp: Kiếm\nĐịnh vị: Cận chiến\nHệ: Lôi\nMức thao tác: Khó\nThầy: Fugaki", "Lớp: Ám khí\nĐịnh vị: Viễn chiến\nHệ: Thổ\nMức thao tác: Dễ\nThầy: Doria", "Lớp: Gậy\nĐịnh vị: Viễn chiến\nHệ: Thủy\nMức thao tác: Khó\nCô: Nohara", "Lớp: Đao\nĐịnh vị: Viễn chiến\nHệ: Hỏa\nMức thao tác: Vừa\nThầy: Nawasaki", "Lớp: Dao\nĐịnh vị: Cận chiến\nHệ: Phong\nMức thao tác: Vừa\nThầy: Ultimo"};
      tI = new String[]{"Set 1", "Set 2", "Set 3", "All"};
      tJ = new String[]{"Mặc định", "Ẩn cải trang", "Ẩn thời trang", "Ẩn cải trang và thời trang"};
   }

   static {
      loadVN();
   }
}
