package beatdz;import beatdz2.*;

import java.util.Comparator;

 class LangLa_fk implements Comparator {
   
   public int compare(Object var1, Object var2) {
      Entity var10000 = (Entity)var1;
      Entity var6 = (Entity)var2;
      Entity var5 = var10000;
      int var3 = var10000.e();
      int var4;
      if ((var4 = var6.e()) == var3) {
         return var5.cy == var6.cy ? var6.cx - var5.cx : var5.cy - var6.cy;
      } else {
         return var4 - var3;
      }
   }
}
