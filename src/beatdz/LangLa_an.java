package beatdz;import beatdz2.*;

import beatdz2.DataCenter;
import com.tgame.model.Caption;
import java.util.Vector;

public class LangLa_an extends LangLa_kj {
   public Item[] a = new Item[2];
   public Item[] b = new Item[6];
   private LangLa_ij[] g = new LangLa_ij[3];
   private LangLa_dz h;
   public int c;
   public int d = 0;
   public LangLa_jw e;
   public boolean f;

   public LangLa_an(MainScreen var1) {
      super(var1, new String[]{Caption.ku, Caption.H});
      this.g[0] = new LangLa_ij((byte)1, 124, this.a_() + 20, 30, 30, 30, 1, 1);
      this.g[1] = new LangLa_ij((byte)1, 14, this.a_() + 20, 64, 96, 32, 3, 2);
      this.g[2] = new LangLa_ij((byte)1, 204, this.a_() + 20, 30, 30, 30, 1, 1);
      this.h = this.a(this.width - 70, this.height - 33, Caption.bj, this, 0, -8);
      this.a(this.h, 0);
   }

   public void a() {
      this.t = new Item[Char.gI().arrItemBag.length];
      int var1 = 0;

      for(int var2 = 0; var2 < Char.gI().arrItemBag.length; ++var2) {
         if (Char.gI().arrItemBag[var2] != null && Char.gI().arrItemBag[var2].H()) {
            this.t[var1] = Char.gI().arrItemBag[var2];
            ++var1;
         }
      }

   }

   public void b() {
      super.b();
      if (this.k() == 0) {
         if (!this.f) {
            if (this.a[0] == null) {
               this.a[1] = null;
            } else {
               this.a[1] = this.a[0].a();
               byte var1 = this.a[1].level;
               this.a[1].a(0);
               this.a[1].isLock = true;
               ItemOption[] var2 = this.a[1].L();
               Vector var3 = new Vector();
               String[] var4 = new String[]{"", "168,10,-1", "169,10,-1", "170,10,-1", "171,10,-1", "172,10,-1"};
               String[] var5 = new String[]{"", "259,80,-1", "260,80,-1", "261,80,-1", "262,80,-1", "263,80,-1"};
               boolean var6 = false;
               boolean var7 = false;

               for(int var8 = 0; var8 < var2.length; ++var8) {
                  if (var2[var8].a[0] != 148) {
                     if (var2[var8].getItemOptionTemplate().type == 2 && !var6) {
                        var3.add(new ItemOption(var4[this.a[1].he]));
                        var3.add(new ItemOption("254,10,-1"));
                        var6 = true;
                     }

                     var3.add(var2[var8]);
                     if (!var7) {
                        if (this.a[1].getItemTemplate().levelNeed / 10 == 4 && var2[var8].getItemOptionTemplate().type == 6) {
                           var3.add(new ItemOption("47,150,-1"));
                           var7 = true;
                        }

                        if (var2[var8].getItemOptionTemplate().type == 7) {
                           if (this.a[1].getItemTemplate().levelNeed / 10 >= 5) {
                              var3.add(new ItemOption("252,5,-1"));
                              var3.add(new ItemOption(var5[this.a[1].he]));
                           }

                           if (this.a[1].getItemTemplate().levelNeed / 10 >= 6) {
                              var3.add(new ItemOption("286,300,-1"));
                           }

                           var7 = true;
                        }
                     }
                  }
               }

               var3.add(new ItemOption("165,0,-1"));
               this.a[1].strOptions = Item.a(var3);
               this.a[1].a(var1);
            }
         }

         for(int var10 = 0; var10 < this.g.length; ++var10) {
            this.g[var10].a();
         }
      } else {
         this.a();
      }

      if (this.e != null) {
         this.e.p();
         if (this.e.r()) {
            this.e = null;
         }
      }

      if (this.d > 0) {
         --this.d;
      }

   }

   public void a(int var1) {
      super.a(var1);
      if (var1 != 0 && this.f) {
         this.f = false;
      }

   }

   public void a(Graphics var1) {
      if (this.j() <= 0) {
         this.a(var1, this.cx, this.cy, this.width, this.height, Caption.q, (byte)2, this.r);
      } else {
         super.a(var1);
      }

      if (this.k() == 0) {
         mFont.b(mFont.d, var1, Caption.kt + DataCenter.gI().ItemTemplate[353].name, 14, this.a_() + 9, 0, -10831436, -16777216);
         mFont.b(mFont.d, var1, Caption.ks, 89, this.a_() + 65, 0, -10831436, -16777216);
         int var2;
         int var3;
         if (this.a[0] != null) {
            var2 = 25000000;
            var3 = this.a[0].getItemTemplate().levelNeed / 10 * 100;
            if (this.a[0].getItemTemplate().levelNeed / 10 == 5) {
               var2 = 30000000;
            } else if (this.a[0].getItemTemplate().levelNeed / 10 == 6) {
               var2 = 40000000;
               var3 = 700;
            }

            mFont.b(mFont.d, var1, Utlis.replaceAll(Caption.kr, Utlis.numberFormat(var3)) + DataCenter.gI().ItemTemplate[353].name, 89, this.a_() + 80, 0, -10831436, -16777216);
            mFont.b(mFont.d, var1, Utlis.replaceAll(Caption.oq, Utlis.numberFormat(var2)), 89, this.a_() + 95, 0, -10831436, -16777216);
         }

         a(var1, this.g[0].cx, this.g[0].cy, this.a[0], this.g[0].k >= 0, Caption.H);
         a(var1, this.g[2].cx, this.g[0].cy, this.a[1], this.g[2].k >= 0, Caption.I);
         LangLa_fq.a(var1, 95, 0, 170 - DataCenter.gI().i / 3 % 3, this.a_() + 25, 20);
         if (this.d > 0 && this.d % 14 > 2) {
            mFont.b(mFont.d, var1, Caption.kL + DataCenter.gI().ItemOptionTemplate[165].name, 14, this.a_() + 125, 0, -2560, -16777216);
         }

         this.a(var1, this.g[1]);
         var2 = 0;

         for(var3 = 0; var3 < this.g[1].i; ++var3) {
            for(int var4 = 0; var4 < this.g[1].r; ++var4) {
               b(var1, var4 * this.g[1].h, var3 * this.g[1].h, this.b[var3 * this.g[1].r + var4], var2 == this.g[1].k, Caption.st[var4]);
               ++var2;
            }
         }

         this.b(var1);
         if (this.e != null) {
            this.e.b(var1, this.g[2].cx + this.g[2].h / 2, this.g[2].cy + this.g[2].h / 2);
         }
      }

   }

   public Vector c() {
      Vector var1 = super.c();
      if (this.k() == 0) {
         for(int var2 = 0; var2 < this.g.length; ++var2) {
            var1.addElement(this.g[var2].a(1001 + var2, this));
         }
      }

      return var1;
   }

   public void a(LangLa_jz var1, int var2, int var3) {
      if (this.k() == this.j() && var1.b == 0) {
         this.d();
      }

      super.a(var1, var2, var3);
      if (this.k() == 0) {
         switch(var1.b) {
         case 0:
            LangLa_an var5 = this;

            try {
               var2 = 0;

               for(var3 = 0; var3 < var5.b.length; ++var3) {
                  if (var5.b[var3] != null) {
                     ++var2;
                  }
               }

               if (var5.a[0] != null && var2 != 0) {
                  Message var6;
                  (var6 = new Message((byte)-35)).writeByte(var5.a[0].TYPE_TEMP);
                  var6.writeShort(var5.a[0].index);
                  var6.writeByte(var2);

                  for(var2 = 0; var2 < var5.b.length; ++var2) {
                     if (var5.b[var2] != null) {
                        var6.writeShort(var5.b[var2].index);
                     }
                  }

                  var6.send();
               } else {
                  DataCenter.gI().currentScreen.showMessage(Caption.kt + DataCenter.gI().ItemTemplate[353].name, -65536);
               }
               break;
            } catch (Exception var4) {
               Utlis.println(var4);
               return;
            }
         case 1001:
            this.c = 1;
            this.a(var1.j, var1.j.k);
            this.cL = a(var1, this, this.a[0]);
            return;
         case 1002:
            this.c = 2;
            this.a(var1.j, var1.j.k);
            this.cL = a(var1, this, this.b[var1.j.k]);
            return;
         case 1003:
            this.c = 3;
            this.a(var1.j, var1.j.k);
            this.cL = a(var1, this, this.a[1]);
         }
      }

   }

   private void a(LangLa_ii var1, int var2) {
      for(int var3 = 0; var3 < this.g.length; ++var3) {
         this.g[var3].k = -1;
      }

      var1.k = var2;
   }

   public void d() {
      super.d();
      if (this.a[0] != null) {
         Char.gI().h(this.a[0].TYPE_TEMP)[this.a[0].index] = this.a[0];
         this.a[0] = null;
      }

      for(int var1 = 0; var1 < this.b.length; ++var1) {
         if (this.b[var1] != null) {
            int var10001 = this.b[var1].index;
            Char.gI().arrItemBag[var10001] = this.b[var1];
            this.b[var1] = null;
         }
      }

   }
}
