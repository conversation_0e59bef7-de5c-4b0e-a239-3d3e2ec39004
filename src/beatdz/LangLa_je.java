package beatdz;import beatdz2.*;

import java.util.Hashtable;

public class Lang<PERSON>a_je {
   public int a;
   public int b;
   public int c;
   public String d;
   public static LangLa_je[] e = new LangLa_je[48];
   public static LangLa_je[] f = new LangLa_je[35];
   public static Lang<PERSON>a_je[] g;
   public static Hashtable h;

   private LangLa_je(int var1, int var2, int var3, String var4) {
      this.a = var1;
      this.c = var2;
      this.b = var3;
      this.d = var4;
   }

   static {
      g = new LangLa_je[e.length + f.length];
      h = new Hashtable();
      e[0] = new LangLa_je(16, 461, 0, ":)");
      e[1] = new LangLa_je(16, 462, 0, "@@");
      e[2] = new LangLa_je(16, 463, 0, ":'(");
      e[3] = new LangLa_je(16, 464, 0, ":o8");
      e[4] = new LangLa_je(16, 465, 0, ":o");
      e[5] = new LangLa_je(16, 466, 0, ":\"(");
      e[6] = new LangLa_je(16, 467, 0, ":|");
      e[7] = new <PERSON>La_je(16, 468, 0, ":d");
      e[8] = new LangLa_je(16, 469, 0, "=(");
      e[9] = new LangLa_je(16, 470, 0, ":\"");
      e[10] = new LangLa_je(16, 471, 0, "*)");
      e[11] = new LangLa_je(16, 472, 0, ":x");
      e[12] = new LangLa_je(16, 473, 0, ":b");
      e[13] = new LangLa_je(16, 474, 0, ">:(");
      e[14] = new LangLa_je(16, 475, 0, ":]");
      e[15] = new LangLa_je(16, 476, 0, ":<");
      e[16] = new LangLa_je(16, 477, 0, "b)");
      e[17] = new LangLa_je(16, 478, 0, ":'<");
      e[18] = new LangLa_je(16, 479, 0, "x=");
      e[19] = new LangLa_je(16, 480, 0, "<3");
      e[20] = new LangLa_je(16, 481, 0, ":[");
      e[21] = new LangLa_je(16, 482, 0, ":\")");
      e[22] = new LangLa_je(16, 483, 0, ":p");
      e[23] = new LangLa_je(16, 484, 0, ":\"d");
      e[24] = new LangLa_je(16, 485, 0, ":')");
      e[25] = new LangLa_je(16, 486, 0, ";)");
      e[26] = new LangLa_je(16, 487, 0, ";(");
      e[27] = new LangLa_je(16, 488, 0, ":.");
      e[28] = new LangLa_je(16, 489, 0, "=d");
      e[29] = new LangLa_je(16, 490, 0, "o:)");
      e[30] = new LangLa_je(16, 491, 0, "b;");
      e[31] = new LangLa_je(16, 492, 0, ":/");
      e[32] = new LangLa_je(16, 493, 0, "~.~");
      e[33] = new LangLa_je(16, 494, 0, "*.*");
      e[34] = new LangLa_je(16, 495, 0, "v.v");
      e[35] = new LangLa_je(12, 924, 0, ":-lc");
      e[36] = new LangLa_je(12, 925, 0, ":-tg");
      e[37] = new LangLa_je(12, 926, 0, ":-tt");
      e[38] = new LangLa_je(12, 927, 0, ":-mp");
      e[39] = new LangLa_je(12, 928, 0, ":-b");
      e[40] = new LangLa_je(12, 929, 0, ":-n");
      e[41] = new LangLa_je(12, 930, 0, ":-r");
      e[42] = new LangLa_je(12, 931, 0, ":-ht");
      e[43] = new LangLa_je(12, 932, 0, ":-sv");
      e[44] = new LangLa_je(22, 504, 0, ":-chat");
      e[45] = new LangLa_je(22, 505, 0, ":-!");
      e[46] = new LangLa_je(22, 506, 0, ":-?");
      e[47] = new LangLa_je(22, 752, 0, ":-loa");
      f[0] = new LangLa_je(0, 0, -1, "c#white");
      f[1] = new LangLa_je(0, 0, -16742145, "c#blue");
      f[2] = new LangLa_je(0, 0, -16777216, "c#black");
      f[3] = new LangLa_je(0, 0, -7812062, "c#green");
      f[4] = new LangLa_je(0, 0, -2560, "c#yellow");
      f[5] = new LangLa_je(0, 0, -48128, "c#orange");
      f[6] = new LangLa_je(0, 0, -3407617, "c#pink");
      f[7] = new LangLa_je(0, 0, -9942667, "c#purple");
      f[8] = new LangLa_je(0, 0, -16711681, "c#cyan");
      f[9] = new LangLa_je(0, 0, -11184811, "c#grey");
      f[10] = new LangLa_je(0, 0, -3089954, "c#silver");
      f[11] = new LangLa_je(0, 0, -2303742, "c#kim");
      f[12] = new LangLa_je(0, 0, -16663468, "c#moc");
      f[13] = new LangLa_je(0, 0, -16744706, "c#thuy");
      f[14] = new LangLa_je(0, 0, -2031616, "c#hoa");
      f[15] = new LangLa_je(0, 0, -4275264, "c#tho");
      f[16] = new LangLa_je(0, 0, -65536, "c#red");
      f[17] = new LangLa_je(0, 0, -11393019, "c#brown");
      f[18] = new LangLa_je(0, 0, -3842196, "c#55f6c");
      f[19] = new LangLa_je(0, 0, -223233, "c#fc97ff");
      f[20] = new LangLa_je(0, 0, -3842196, "c#c55f6c");
      f[21] = new LangLa_je(0, 0, -8214083, "c#lc");
      f[22] = new LangLa_je(0, 0, -13590693, "c#tg");
      f[23] = new LangLa_je(0, 0, -3421237, "c#tt");
      f[24] = new LangLa_je(0, 0, -6707504, "c#mp");
      f[25] = new LangLa_je(0, 0, -4273769, "c#bg");
      f[26] = new LangLa_je(0, 0, -3493470, "c#nm");
      f[27] = new LangLa_je(0, 0, -3040351, "c#ht");
      f[28] = new LangLa_je(0, 0, -5347356, "c#rg");
      f[29] = new LangLa_je(0, 0, -350553, "c#me");
      f[30] = new LangLa_je(0, 0, -10831436, "c#5ab9b4");
      f[31] = new LangLa_je(0, 0, -15391780, "c#sv");
      f[32] = new LangLa_je(0, 0, -7812062, "c#task");
      f[33] = new LangLa_je(0, 0, -16711681, "c#item");
      f[34] = new LangLa_je(0, 0, -2560, "c#select");

      int var0;
      for(var0 = 0; var0 < e.length; ++var0) {
         g[var0] = e[var0];
         h.put(g[var0].d, g[var0]);
      }

      for(var0 = 0; var0 < f.length; ++var0) {
         g[var0 + e.length] = f[var0];
         h.put(g[var0 + e.length].d, g[var0 + e.length]);
      }

   }
}
