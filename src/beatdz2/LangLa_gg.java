package beatdz2;import beatdz.*;

public class LangLa_gg {
   public boolean a = true;
   public int b;
   public int c;
   private int g;
   public int d;
   private int h;
   private int i;
   public static mImage e;
   public boolean f;

   public LangLa_gg(MainScreen var1) {
      e = Binary2.a("n", var1);
   }

   public void a(int var1, int var2) {
      int var3 = var1 + GameSrc.gI().Task + GameSrc.gI().idStep;
      if (this.d == 2) {
         var3 = var1 + GameSrc.gI().Task + GameSrc.gI().idStep + GameSrc.gI().idRequire;
      }

      if (this.g != var3 && (GameSrc.gI().Task <= 12 || GameSrc.gI().Task == 14) && (GameSrc.gI().Task != this.d || GameSrc.gI().idStep != this.h || GameSrc.gI().Task == 2 && GameSrc.gI().idRequire != this.i || GameSrc.gI().Task == this.d && GameSrc.gI().idStep == this.h && this.b != var1 && this.c != var2)) {
         this.f = true;
         this.g = var3;
         this.d = GameSrc.gI().Task;
         this.h = GameSrc.gI().idStep;
         this.i = GameSrc.gI().idRequire;
      }

      this.b = var1;
      this.c = var2;
   }
}
